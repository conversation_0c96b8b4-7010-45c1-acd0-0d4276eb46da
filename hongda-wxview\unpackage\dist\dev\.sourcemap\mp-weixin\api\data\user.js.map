{"version": 3, "file": "user.js", "sources": ["api/data/user.js"], "sourcesContent": ["import { post, del } from '@/utils/request.js';\r\n\r\n// 微信登录接口\r\nexport const wxLoginApi = (code) => {\r\n  return post('/wxlogin', { code });\r\n};\r\n\r\n// 获取手机号接口\r\nexport const getPhoneNumberApi = (code) => {\r\n  return post('/getPhoneNumber', { code });\r\n};\r\n\r\n// 注销账号接口\r\nexport const deleteAccountApi = () => {\r\n  return del('/user/delete');\r\n};\r\n\r\n// 获取用户信息接口\r\nexport const getUserInfoApi = () => {\r\n  return post('/user/info');\r\n};\r\n\r\n// 更新用户信息接口\r\nexport const updateUserInfoApi = (data) => {\r\n  return post('/user/update', data);\r\n};\r\n\r\n// 绑定手机号接口\r\nexport const bindPhoneApi = (code) => {\r\n  return post('/user/bind-phone', { code });\r\n}; "], "names": ["post", "del"], "mappings": ";;AAGY,MAAC,aAAa,CAAC,SAAS;AAClC,SAAOA,mBAAK,YAAY,EAAE,KAAM,CAAA;AAClC;AAGY,MAAC,oBAAoB,CAAC,SAAS;AACzC,SAAOA,mBAAK,mBAAmB,EAAE,KAAM,CAAA;AACzC;AAGY,MAAC,mBAAmB,MAAM;AACpC,SAAOC,cAAAA,IAAI,cAAc;AAC3B;AAQY,MAAC,oBAAoB,CAAC,SAAS;AACzC,SAAOD,cAAI,KAAC,gBAAgB,IAAI;AAClC;;;;;"}