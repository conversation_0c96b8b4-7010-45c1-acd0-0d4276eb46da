<template>
  <view class="event-list-page">
    <!-- 1. 头部背景和导航栏区域 -->
    <view class="header-wrapper">
      <!-- 背景图片 - 完全覆盖状态栏 -->
      <image class="header-bg" src="/static/event/eventbg.png" mode="aspectFill"></image>

      <!-- 自定义导航栏 -->
      <view class="custom-navbar">
        <view class="navbar-left" @click="goBack">
          <up-icon name="arrow-left" size="24" color="#FFFFFF"></up-icon>
        </view>
        <view class="navbar-title">
          <text class="title-text">热门活动列表</text>
        </view>
        <view class="navbar-right">
          <!-- 移除更多按钮 -->
        </view>
      </view>

      <!-- 第一行 (Subsection + Search Bar) -->
      <view class="top-controls">
        <up-subsection :list="['列表', '日历']" :current="currentTab" @change="tabChange" mode="subsection"
                       activeColor="#f56c6c"></up-subsection>

        <view class="search-wrapper">
          <CustomSearchBox
              v-model="searchKeyword"
              placeholder="搜索活动"
              @search="onSearch"
              @input="debouncedSearch"
          ></CustomSearchBox>
        </view>
      </view>
    </view>

    <!-- 2.1 列表视图的筛选栏 (移到scroll-view外部) -->
    <view v-if="currentTab === 0" class="filter-bar sticky-filter-bar">
      <!-- 筛选按钮组 -->
      <view class="filter-main-buttons">
        <!-- 综合排序按钮 -->
        <view class="filter-button" @click="toggleSortPanel">
          <text class="filter-text">{{ getCurrentSortTitle }}</text>
          <up-icon name="arrow-down" size="14" color="#666" :class="{ 'rotate-180': showSortPanel }"></up-icon>
        </view>
        <!-- 全部地区按钮 -->
        <view class="filter-button" @click="toggleLocationPanel">
          <text class="filter-text">{{ getCurrentLocationTitle }}</text>
          <up-icon name="arrow-down" size="14" color="#666" :class="{ 'rotate-180': showLocationPanel }"></up-icon>
        </view>
        <!-- 全部时间按钮 -->
        <view class="filter-button" @click="toggleTimePanel">
          <text class="filter-text">{{ getCurrentTimeTitle }}</text>
          <up-icon name="arrow-down" size="14" color="#666" :class="{ 'rotate-180': showTimePanel }"></up-icon>
        </view>
        <!-- 全部状态按钮 -->
        <view class="filter-button" @click="toggleStatusPanel">
          <text class="filter-text">{{ getCurrentStatusTitle }}</text>
          <up-icon name="arrow-down" size="14" color="#666" :class="{ 'rotate-180': showStatusPanel }"></up-icon>
        </view>
      </view>

      <!-- 综合排序面板 -->
      <view v-if="showSortPanel" class="filter-panel">
        <text class="section-title">排序</text>
        <view class="option-grid">
          <view
              v-for="option in options1"
              :key="option.value"
              :class="['option-item', { 'active': tempFilters.sortBy === option.value }]"
              @click="selectSortOption(option.value)"
          >
            <text class="option-text">{{ option.label }}</text>
          </view>
        </view>
        <!-- 重置和完成按钮 -->
        <view class="filter-buttons">
          <view class="filter-btn reset-btn" @click="resetSortFilter">
            <text class="btn-text">重置</text>
          </view>
          <view class="filter-btn complete-btn" @click="completeSortFilter">
            <text class="btn-text">完成</text>
          </view>
        </view>
      </view>

      <!-- 地区筛选面板 -->
      <view v-if="showLocationPanel" class="filter-panel">
        <text class="section-title">所属地区</text>
        <view class="option-grid">
          <view
              v-for="option in options2"
              :key="option.value"
              :class="['option-item', { 'active': tempFilters.location === option.value }]"
              @click="selectLocationOption(option.value)"
          >
            <text class="option-text">{{ option.label }}</text>
          </view>
        </view>
        <!-- 重置和完成按钮 -->
        <view class="filter-buttons">
          <view class="filter-btn reset-btn" @click="resetLocationFilter">
            <text class="btn-text">重置</text>
          </view>
          <view class="filter-btn complete-btn" @click="completeLocationFilter">
            <text class="btn-text">完成</text>
          </view>
        </view>
      </view>

      <!-- 时间筛选面板 -->
      <view v-if="showTimePanel" class="filter-panel">
        <text class="section-title">时间</text>
        <view class="option-grid">
          <view
              v-for="option in options3"
              :key="option.value"
              :class="['option-item', { 'active': tempFilters.timeRange === option.value }]"
              @click="selectTimeOption(option.value)"
          >
            <text class="option-text">{{ option.label }}</text>
          </view>
        </view>
        <!-- 重置和完成按钮 -->
        <view class="filter-buttons">
          <view class="filter-btn reset-btn" @click="resetTimeFilter">
            <text class="btn-text">重置</text>
          </view>
          <view class="filter-btn complete-btn" @click="completeTimeFilter">
            <text class="btn-text">完成</text>
          </view>
        </view>
      </view>

      <!-- 状态筛选面板 -->
      <view v-if="showStatusPanel" class="filter-panel">
        <text class="section-title">全部状态</text>
        <view class="option-grid">
          <view
              v-for="option in options4"
              :key="option.value"
              :class="['option-item', { 'active': tempFilters.status === option.value }]"
              @click="selectStatusOption(option.value)"
          >
            <text class="option-text">{{ option.label }}</text>
          </view>
        </view>
        <!-- 重置和完成按钮 -->
        <view class="filter-buttons">
          <view class="filter-btn reset-btn" @click="resetStatusFilter">
            <text class="btn-text">重置</text>
          </view>
          <view class="filter-btn complete-btn" @click="completeStatusFilter">
            <text class="btn-text">完成</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 2.2 活动列表视图 -->
    <scroll-view v-if="currentTab === 0" scroll-y class="event-list-scroll list-scroll-with-filter"
                 @scrolltolower="onLoadMore"
                 refresher-enabled :refresher-triggered="isRefreshing" @refresherrefresh="onRefresh">

      <!-- 空状态 -->
      <view v-if="!isLoading && eventList.length === 0" class="empty-state">
        <up-empty mode="data" text="暂无活动数据" textColor="#909399" iconSize="120"></up-empty>

        <!-- 重试按钮 -->
        <view v-if="showRetry" class="retry-container">
          <up-button type="primary" size="normal" @click="fetchEventList">
            重新加载
          </up-button>
        </view>
      </view>

      <!-- 活动卡片列表 -->
      <view v-for="event in eventList" :key="event.id" class="event-card" @click="goToDetail(event)">
        <view class="card-left">
          <image :src="getFullImageUrl(event.coverImageUrl)" mode="aspectFill" class="event-image"
                 :lazy-load="true"></image>
          <view :class="['status-tag', getStatusClass(event.status)]">
            {{ formatEventStatus(event.status) }}
          </view>
        </view>

        <view class="card-right">
          <text class="event-title">{{ event.title }}</text>

          <view class="event-info-row">
            <view class="time-location-item">
              <image class="event-info-icon" src="/static/event/时间icon灰@2x.png" mode="aspectFit"></image>
              <text class="info-text">{{ formatEventDate(event.startTime) }}</text>
            </view>
            <view class="time-location-item">
              <image class="event-info-icon" src="/static/event/位置icon灰@2x.png" mode="aspectFit"></image>
              <text class="info-text">{{ formatEventLocation(event) }}</text>
            </view>
          </view>

          <view class="event-info remaining-spots">
            <text class="spots-count">
              剩余名额: {{ calculateRemainingSpots(event.maxParticipants, event.registeredCount) }}
            </text>
          </view>
        </view>
      </view>

      <!-- 加载更多组件 -->
      <view class="loadmore-wrapper">
        <up-loadmore :status="loadMoreStatus" :loading-text="'正在加载...'" :loadmore-text="'上拉加载更多'"
                     :nomore-text="'没有更多了'"/>
      </view>
    </scroll-view>

    <!-- 2.3 日历视图的筛选栏 -->
    <view v-if="currentTab === 1" class="filter-bar calendar-filter-bar sticky-filter-bar">
      <!-- 筛选按钮组 - 只显示地区和时间 -->
      <view class="filter-main-buttons calendar-filter-buttons">
        <!-- 全部地区按钮 -->
        <view class="filter-button" @click="toggleLocationPanel">
          <text class="filter-text">{{ getCurrentLocationTitle }}</text>
          <up-icon name="arrow-down" size="14" color="#666" :class="{ 'rotate-180': showLocationPanel }"></up-icon>
        </view>
        <!-- 全部时间按钮 -->
        <view class="filter-button" @click="toggleTimePanel">
          <text class="filter-text">{{ getCurrentTimeTitle }}</text>
          <up-icon name="arrow-down" size="14" color="#666" :class="{ 'rotate-180': showTimePanel }"></up-icon>
        </view>
      </view>

      <!-- 地区筛选面板 -->
      <view v-if="showLocationPanel" class="filter-panel">
        <text class="section-title">所属地区</text>
        <view class="option-grid">
          <view
              v-for="option in options2"
              :key="option.value"
              :class="['option-item', { 'active': tempFilters.location === option.value }]"
              @click="selectLocationOption(option.value)"
          >
            <text class="option-text">{{ option.label }}</text>
          </view>
        </view>
        <!-- 重置和完成按钮 -->
        <view class="filter-buttons">
          <view class="filter-btn reset-btn" @click="resetLocationFilter">
            <text class="btn-text">重置</text>
          </view>
          <view class="filter-btn complete-btn" @click="completeLocationFilter">
            <text class="btn-text">完成</text>
          </view>
        </view>
      </view>

      <!-- 时间筛选面板 -->
      <view v-if="showTimePanel" class="filter-panel">
        <text class="section-title">时间</text>
        <view class="option-grid">
          <view
              v-for="option in options3"
              :key="option.value"
              :class="['option-item', { 'active': tempFilters.timeRange === option.value }]"
              @click="selectTimeOption(option.value)"
          >
            <text class="option-text">{{ option.label }}</text>
          </view>
        </view>
        <!-- 重置和完成按钮 -->
        <view class="filter-buttons">
          <view class="filter-btn reset-btn" @click="resetTimeFilter">
            <text class="btn-text">重置</text>
          </view>
          <view class="filter-btn complete-btn" @click="completeTimeFilter">
            <text class="btn-text">完成</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 2.4 日历视图 -->
    <scroll-view v-if="currentTab === 1" scroll-y class="event-list-scroll calendar-view calendar-scroll-with-filter"
                 @scrolltolower="onLoadMore" refresher-enabled :refresher-triggered="isRefreshing"
                 @refresherrefresh="onRefresh">

      <!-- 空状态 -->
      <view v-if="!isLoading && limitedCalendarEvents.length === 0" class="empty-state">
        <up-empty mode="data" text="暂无活动数据" textColor="#909399" iconSize="120"></up-empty>

        <!-- 重试按钮 -->
        <view v-if="showRetry" class="retry-container">
          <up-button type="primary" size="normal" @click="fetchEventList">
            重新加载
          </up-button>
        </view>
      </view>

      <!-- 垂直时间轴布局 - 根据UI设计稿规范 -->
      <view v-else class="vertical-timeline">
        <view v-for="(group, index) in limitedCalendarEvents" :key="group.date" class="date-section">
          <!-- 日期标题和圆点 -->
          <view class="date-header">
            <!-- 中心圆点 -->
            <view class="timeline-dot"></view>
            <!-- 日期信息 -->
            <text class="time-text">{{ group.formattedDate }}</text>
            <text class="weekday-text">{{ group.dayOfWeek }}</text>

          </view>

          <!-- 垂直连接线 -->
          <view class="line-connector" v-if="index < limitedCalendarEvents.length - 1">
            <view class="timeline-line"></view>
          </view>

          <!-- 当天活动卡片列表 -->
          <view class="events-container">
            <view v-for="event in group.events" :key="event.id" class="compact-event-card" @click="goToDetail(event)">
              <!-- 左侧Logo -->
              <image :src="getFullImageUrl(event.iconUrl)" class="event-avatar" mode="aspectFill"></image>
              <!-- 活动内容 -->
              <view class="event-content">
                <text class="event-title-compact">{{ event.title }}</text>
                <view class="location-group">
                  <view class="separator-line"></view>
                  <view class="event-location-compact">
                    <image class="location-icon" src="/static/event/golden-location-icon.png" mode="aspectFit"></image>
                    <text class="location-text">{{ formatEventLocation(event) }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 查看更多活动按钮 -->
        <view v-if="hasMoreCalendarEvents" class="view-more-button" @click="switchToListView">
          <text class="button-text">查看更多活动</text>
        </view>
      </view>


    </scroll-view>

    <!-- 自定义底部导航栏 -->
    <CustomTabBar :current="2"/>
  </view>
</template>

<script setup>
import {
  ref,
  computed
} from 'vue';
import {
  onLoad,
  onReachBottom,
  onPullDownRefresh,
  onUnload,
  onShow
} from '@dcloudio/uni-app';
import CustomTabBar from '@/components/layout/CustomTabBar.vue';
import CustomSearchBox from '@/components/home/<USER>';
import {
  getEventListApi,
  getCalendarEventsApi
} from '@/api/data/event.js';
import {
  formatEventStatus,
  getStatusClass,
  calculateRemainingSpots,
  debounce
} from '@/utils/tools.js';
import {
  formatEventDate
} from '@/utils/date.js';
import {
  PAGE_CONFIG
} from '@/utils/config.js';
import {getFullImageUrl} from '@/utils/image.js';

// ==================== 响应式数据定义 ====================
const currentTab = ref(0); // 默认显示列表视图
const searchKeyword = ref('');
const eventList = ref([]);
const isLoading = ref(false);
const isRefreshing = ref(false);
const showRetry = ref(false);

// 分页相关
const pagination = ref({
  pageNum: 1,
  pageSize: PAGE_CONFIG.DEFAULT_PAGE_SIZE,
  total: 0,
  hasMore: true
});

// 已应用的筛选条件（用于实际数据请求）
const appliedFilters = ref({
  sortBy: 1,
  location: 1,
  timeRange: 1,
  status: 1
});

// 临时筛选条件（用于UI显示，未应用到数据）
const tempFilters = ref({
  sortBy: 1,
  location: 1,
  timeRange: 1,
  status: 1
});

// 控制各个筛选面板显示
const showSortPanel = ref(false);
const showLocationPanel = ref(false);
const showTimePanel = ref(false);
const showStatusPanel = ref(false);

// 日历视图显示控制 - 移除showAllCalendarEvents，因为现在按钮直接跳转到列表视图

// 下拉选项配置
const options1 = ref([{
  label: '综合排序',
  value: 1
},
  {
    label: '最新发布',
    value: 2

  },
  {
    label: '最近开始',
    value: 3
  }
]);

const options2 = ref([{
  label: '全部地区',
  value: 1
},
  {
    label: '北京',
    value: 2
  },
  {
    label: '上海',
    value: 3
  },
  {
    label: '厦门',
    value: 4
  },
  {
    label: '广州',
    value: 5
  }
]);

const options3 = ref([{
  label: '全部时间',
  value: 1
},
  {
    label: '1周内',
    value: 2
  },
  {
    label: '1月内',
    value: 3
  },
  {
    label: '1年内',
    value: 4
  }
]);

const options4 = ref([{
  label: '全部状态',
  value: 1
},
  {
    label: '报名中',
    value: 2
  },
  {
    label: '已结束',
    value: 3
  }
]);

// ==================== 计算属性 ====================
const loadMoreStatus = computed(() => {
  if (isLoading.value) return 'loading';
  if (!pagination.value.hasMore) return 'nomore';
  return 'more';
});

/**
 * 按日期分组活动数据 - 用于日历视图
 */
const groupedEvents = computed(() => {
  if (!eventList.value || eventList.value.length === 0) {
    return [];
  }
  const groups = new Map();
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  eventList.value.forEach(event => {
    const eventDate = new Date(event.startTime);
    // 💥 关键修改：处理不同年份的日期显示
    const year = eventDate.getFullYear();
    const currentYear = new Date().getFullYear();
    const month = String(eventDate.getMonth() + 1).padStart(2, '0');
    const day = String(eventDate.getDate()).padStart(2, '0');

    let displayDate;
    if (year !== currentYear) {
      displayDate = `${year}年${month}月${day}日`;
    } else {
      displayDate = `${month}.${day}`;
    }

    const dateKey = eventDate.toISOString().split('T')[0];
    if (!groups.has(dateKey)) {
      groups.set(dateKey, {
        date: dateKey,
        formattedDate: displayDate, // 使用新的日期格式
        dayOfWeek: weekdays[eventDate.getDay()],
        events: []
      });
    }
    groups.get(dateKey).events.push(event);
  });
  return Array.from(groups.values());
});

/**
 * 限制日历视图显示的活动数量 - 最多显示10个活动
 */
const limitedCalendarEvents = computed(() => {
  // 日历视图始终限制为最多10个活动
  let totalEvents = 0;
  const limitedGroups = [];

  for (const group of groupedEvents.value) {
    if (totalEvents >= 10) break;

    const remainingSlots = 10 - totalEvents;
    const eventsToShow = group.events.slice(0, remainingSlots);

    limitedGroups.push({
      ...group,
      events: eventsToShow
    });

    totalEvents += eventsToShow.length;
  }

  return limitedGroups;
});

/**
 * 检查是否有更多活动需要显示
 */
const hasMoreCalendarEvents = computed(() => {
  const totalEvents = groupedEvents.value.reduce((sum, group) => sum + group.events.length, 0);
  return totalEvents > 10;
});

// ==================== 核心方法 ====================

/**
 * 格式化活动地点 - 只显示城市
 * 与精选活动组件保持一致
 */
const formatEventLocation = (event) => {
  // 只使用城市字段
  if (event.city && event.city.trim()) {
    return event.city.trim().replace(/市$/, '');
  }

  // 如果没有城市，显示待定
  return '待定';
};
/**
 * 构建查询参数
 */
const buildQueryParams = (isLoadMore = false) => {
  const params = {
    pageNum: isLoadMore ? pagination.value.pageNum : 1,
    pageSize: pagination.value.pageSize
  };

  // 搜索关键词
  if (searchKeyword.value.trim()) {
    params.title = searchKeyword.value.trim();
  }

  // 地区筛选（使用已应用的筛选条件）
  if (appliedFilters.value.location > 1) {
    const locationMap = {
      2: '北京',
      3: '上海',
      4: '厦门',
      5: '广州'
    };
    params.location = locationMap[appliedFilters.value.location];
  }

  // 状态筛选（使用已应用的筛选条件）
  if (appliedFilters.value.status > 1) {
    const statusMap = {
      2: 1, // 报名中
      3: 2  // 已结束
    };

    if (statusMap.hasOwnProperty(appliedFilters.value.status)) {
      params.status = statusMap[appliedFilters.value.status];
      console.log('🔍 状态筛选:', `前端值${appliedFilters.value.status} -> 数据库值${params.status}`);
    } else {
      console.warn('⚠️ 未知的状态筛选值:', appliedFilters.value.status);
    }
  }

  // 排序设置
  if (currentTab.value === 1) {
    // 如果是日历视图，强制按开始时间升序排序
    params.orderBy = 'startTime';
    params.isAsc = 'asc';
    console.log('📅 日历视图排序: 按开始时间升序');
  } else {
    // 列表视图排序逻辑 - 支持综合排序（使用已应用的筛选条件）
    switch (appliedFilters.value.sortBy) {
      case 1: // 综合排序
        params.orderBy = 'comprehensive';
        // 综合排序不需要指定isAsc，由后端算法决定
        console.log('综合排序: 智能多维度排序');
        break;
      case 2: // 按时间（最近开始）
        params.orderBy = 'startTime';
        params.isAsc = 'asc';
        console.log('按时间排序: 最近开始优先');
        break;
      case 3: // 按最新发布
        params.orderBy = 'createTime';
        params.isAsc = 'desc';
        console.log('按最新发布排序: 最新创建的活动优先');
        break;
      default: // 默认按创建时间（最新发布）
        params.orderBy = 'createTime';
        params.isAsc = 'desc';
        console.log('默认排序: 最新发布');
    }
  }

  // 时间范围筛选 - 新的时间筛选逻辑（使用已应用的筛选条件）
  if (appliedFilters.value.timeRange > 1) {
    const timeRange = buildTimeRangeParams(appliedFilters.value.timeRange);
    if (timeRange) {
      Object.assign(params, timeRange);
    }
  }

  return params;
};

/**
 * 构建时间范围筛选参数 - 独立的时间筛选逻辑
 * @param {number} timeRangeValue 时间范围选项值
 * @returns {object|null} 时间筛选参数对象
 */
const buildTimeRangeParams = (timeRangeValue) => {
  const now = new Date();
  let startTime = null;
  let endTime = null;

  switch (timeRangeValue) {
    case 2: // 1周内
      startTime = now;
      endTime = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
      console.log('时间筛选: 1周内 -', startTime.toLocaleDateString(), '到', endTime.toLocaleDateString());
      break;
    case 3: // 1月内
      startTime = now;
      endTime = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
      console.log('时间筛选: 1月内 -', startTime.toLocaleDateString(), '到', endTime.toLocaleDateString());
      break;
    case 4: // 1年内
      startTime = now;
      endTime = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());
      console.log('时间筛选: 1年内 -', startTime.toLocaleDateString(), '到', endTime.toLocaleDateString());
      break;
    default:
      console.log('时间筛选: 全部时间');
      return null;
  }

  return {
    timeRangeStart: startTime.toISOString(),
    timeRangeEnd: endTime.toISOString()
  };
};

/**
 * 获取活动列表
 */
const fetchEventList = async (isLoadMore = false) => {
  if (isLoading.value) return;

  isLoading.value = true;

  try {
    const params = buildQueryParams(isLoadMore);
    console.log('请求参数:', params);

    // 根据当前视图选择不同的API
    let response;
    if (currentTab.value === 1) {
      // 日历视图使用专门的API
      console.log('使用日历视图专用API');
      response = await getCalendarEventsApi(params);
    } else {
      // 列表视图使用通用API
      console.log('使用列表视图通用API');
      response = await getEventListApi(params);
    }
    const {
      rows = [], total = 0
    } = response;

    if (isLoadMore) {
      eventList.value.push(...rows);
    } else {
      eventList.value = rows;
      pagination.value.pageNum = 1;
    }

    pagination.value.total = total;
    pagination.value.hasMore = eventList.value.length < total;

    // 成功加载后隐藏重试按钮
    showRetry.value = false;

    console.log(`获取活动列表成功: ${rows.length} 条记录, 总计: ${total}`);

  } catch (error) {
    console.error('获取活动列表失败:', error);

    // 根据错误类型提供不同的提示
    let errorMessage = '获取活动列表失败';
    if (error.message && error.message.includes('timeout')) {
      errorMessage = '网络请求超时，请重试';
    } else if (error.message && error.message.includes('Network')) {
      errorMessage = '网络连接失败，请检查网络';
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000
    });

    // 如果是首次加载失败，显示重试按钮
    if (!isLoadMore && eventList.value.length === 0) {
      showRetry.value = true;
    }
  } finally {
    isLoading.value = false;
    isRefreshing.value = false;
  }
};

/**
 * 搜索防抖处理
 */
const debouncedSearch = debounce(() => {
  fetchEventList();
}, 500);

// ==================== 计算属性 ====================
/**
 * 获取当前排序方式的显示标题（基于已应用的筛选条件）
 */
const getCurrentSortTitle = computed(() => {
  const currentOption = options1.value.find(option => option.value === appliedFilters.value.sortBy);
  const title = currentOption ? currentOption.label : '综合排序';
  console.log('当前排序标题:', title, '排序值:', appliedFilters.value.sortBy);
  return title;
});

/**
 * 获取当前状态筛选的显示标题（基于已应用的筛选条件）
 */
const getCurrentStatusTitle = computed(() => {
  const currentOption = options4.value.find(option => option.value === appliedFilters.value.status);
  const title = currentOption ? currentOption.label : '全部状态';
  console.log('当前状态标题:', title, '状态值:', appliedFilters.value.status);
  return title;
});

/**
 * 获取当前地区筛选的显示标题（基于已应用的筛选条件）
 */
const getCurrentLocationTitle = computed(() => {
  const currentOption = options2.value.find(option => option.value === appliedFilters.value.location);
  return currentOption ? currentOption.label : '全部地区';
});

/**
 * 获取当前时间筛选的显示标题（基于已应用的筛选条件）
 */
const getCurrentTimeTitle = computed(() => {
  const currentOption = options3.value.find(option => option.value === appliedFilters.value.timeRange);
  return currentOption ? currentOption.label : '全部时间';
});

// ==================== 事件处理方法 ====================
/**
 * 返回按钮处理
 */
const goBack = () => {
  uni.navigateBack({
    fail: () => {
      // 如果没有上一页，则跳转到首页
      uni.switchTab({
        url: '/pages/index/index'
      });
    }
  });
};

/**
 * 标签页切换
 */
const tabChange = (index) => {
  currentTab.value = index; // 直接使用 index，因为 u-subsection 的 @change 事件直接返回索引号
  // 关键：切换后立即重置数据，以保证用户看到即时加载效果
  eventList.value = [];
  pagination.value.pageNum = 1;
  pagination.value.hasMore = true;
  // 重新调用数据获取函数，此时 buildQueryParams 会根据新的 currentTab 值应用正确的排序
  fetchEventList();

  console.log('视图切换到:', index === 0 ? '列表视图' : '日历视图');
};

/**
 * 搜索处理
 */
const onSearch = (value) => {
  searchKeyword.value = value;
  debouncedSearch();
};

/**
 * 筛选条件变更
 */
const onFilterChange = () => {
  console.log('筛选条件变更，重置数据并重新加载');
  // 重置分页状态
  eventList.value = [];
  pagination.value.pageNum = 1;
  pagination.value.hasMore = true;
  // 重新获取数据
  fetchEventList();
};

/**
 * 切换排序面板显示状态
 */
const toggleSortPanel = () => {
  if (!showSortPanel.value) {
    // 打开面板时，同步当前已应用的筛选条件到临时筛选条件
    tempFilters.value.sortBy = appliedFilters.value.sortBy;
  }
  showSortPanel.value = !showSortPanel.value;
  // 关闭其他面板
  showLocationPanel.value = false;
  showTimePanel.value = false;
  showStatusPanel.value = false;
};

/**
 * 切换地区面板显示状态
 */
const toggleLocationPanel = () => {
  if (!showLocationPanel.value) {
    // 打开面板时，同步当前已应用的筛选条件到临时筛选条件
    tempFilters.value.location = appliedFilters.value.location;
  }
  showLocationPanel.value = !showLocationPanel.value;
  // 关闭其他面板
  showSortPanel.value = false;
  showTimePanel.value = false;
  showStatusPanel.value = false;
};

/**
 * 切换时间面板显示状态
 */
const toggleTimePanel = () => {
  if (!showTimePanel.value) {
    // 打开面板时，同步当前已应用的筛选条件到临时筛选条件
    tempFilters.value.timeRange = appliedFilters.value.timeRange;
  }
  showTimePanel.value = !showTimePanel.value;
  // 关闭其他面板
  showSortPanel.value = false;
  showLocationPanel.value = false;
  showStatusPanel.value = false;
};

/**
 * 切换状态面板显示状态
 */
const toggleStatusPanel = () => {
  if (!showStatusPanel.value) {
    // 打开面板时，同步当前已应用的筛选条件到临时筛选条件
    tempFilters.value.status = appliedFilters.value.status;
  }
  showStatusPanel.value = !showStatusPanel.value;
  // 关闭其他面板
  showSortPanel.value = false;
  showLocationPanel.value = false;
  showTimePanel.value = false;
};

/**
 * 选择排序选项（仅更新临时筛选条件，不立即应用）
 */
const selectSortOption = (value) => {
  tempFilters.value.sortBy = value;
  console.log('临时选择排序:', value);
  // 不关闭面板，不立即应用筛选
};

/**
 * 选择地区选项（仅更新临时筛选条件，不立即应用）
 */
const selectLocationOption = (value) => {
  tempFilters.value.location = value;
  console.log('临时选择地区:', value);
  // 不关闭面板，不立即应用筛选
};

/**
 * 选择时间选项（仅更新临时筛选条件，不立即应用）
 */
const selectTimeOption = (value) => {
  tempFilters.value.timeRange = value;
  console.log('临时选择时间:', value);
  // 不关闭面板，不立即应用筛选
};

/**
 * 选择状态选项（仅更新临时筛选条件，不立即应用）
 */
const selectStatusOption = (value) => {
  tempFilters.value.status = value;
  console.log('临时选择状态:', value);
  // 不关闭面板，不立即应用筛选
};

/**
 * 重置排序筛选（重置为页面初始状态并立即应用）
 */
const resetSortFilter = () => {
  // 重置临时筛选条件为初始值
  tempFilters.value.sortBy = 1;
  // 立即应用重置后的筛选条件
  appliedFilters.value.sortBy = 1;
  showSortPanel.value = false;
  console.log('重置排序筛选为初始状态');
  onFilterChange();
};

/**
 * 完成排序筛选（应用临时筛选条件并关闭面板）
 */
const completeSortFilter = () => {
  // 将临时筛选条件应用到已应用筛选条件
  appliedFilters.value.sortBy = tempFilters.value.sortBy;
  showSortPanel.value = false;
  console.log('应用排序筛选:', tempFilters.value.sortBy);
  onFilterChange();
};

/**
 * 重置地区筛选（重置为页面初始状态并立即应用）
 */
const resetLocationFilter = () => {
  // 重置临时筛选条件为初始值
  tempFilters.value.location = 1;
  // 立即应用重置后的筛选条件
  appliedFilters.value.location = 1;
  showLocationPanel.value = false;
  console.log('重置地区筛选为初始状态');
  onFilterChange();
};

/**
 * 完成地区筛选（应用临时筛选条件并关闭面板）
 */
const completeLocationFilter = () => {
  // 将临时筛选条件应用到已应用筛选条件
  appliedFilters.value.location = tempFilters.value.location;
  showLocationPanel.value = false;
  console.log('应用地区筛选:', tempFilters.value.location);
  onFilterChange();
};

/**
 * 重置时间筛选（重置为页面初始状态并立即应用）
 */
const resetTimeFilter = () => {
  // 重置临时筛选条件为初始值
  tempFilters.value.timeRange = 1;
  // 立即应用重置后的筛选条件
  appliedFilters.value.timeRange = 1;
  showTimePanel.value = false;
  console.log('重置时间筛选为初始状态');
  onFilterChange();
};

/**
 * 完成时间筛选（应用临时筛选条件并关闭面板）
 */
const completeTimeFilter = () => {
  // 将临时筛选条件应用到已应用筛选条件
  appliedFilters.value.timeRange = tempFilters.value.timeRange;
  showTimePanel.value = false;
  console.log('应用时间筛选:', tempFilters.value.timeRange);
  onFilterChange();
};

/**
 * 重置状态筛选（重置为页面初始状态并立即应用）
 */
const resetStatusFilter = () => {
  // 重置临时筛选条件为初始值
  tempFilters.value.status = 1;
  // 立即应用重置后的筛选条件
  appliedFilters.value.status = 1;
  showStatusPanel.value = false;
  console.log('重置状态筛选为初始状态');
  onFilterChange();
};

/**
 * 完成状态筛选（应用临时筛选条件并关闭面板）
 */
const completeStatusFilter = () => {
  // 将临时筛选条件应用到已应用筛选条件
  appliedFilters.value.status = tempFilters.value.status;
  showStatusPanel.value = false;
  console.log('应用状态筛选:', tempFilters.value.status);
  onFilterChange();
};

/**
 * 跳转到活动详情
 */
const goToDetail = (event) => {
  uni.navigateTo({
    url: `/pages_sub/pages_event/detail?id=${event.id}`
  });
};

/**
 * 切换到列表视图 - 查看更多活动按钮点击处理
 */
const switchToListView = () => {
  currentTab.value = 0; // 切换到列表视图
  console.log('从日历视图切换到列表视图 - 查看更多活动');
  // 切换后重新获取数据以确保列表视图显示所有活动
  fetchEventList();
};

/**
 * 下拉刷新
 */
const onRefresh = () => {
  isRefreshing.value = true;
  pagination.value.pageNum = 1;
  fetchEventList();
};

/**
 * 上拉加载更多
 */
const onLoadMore = () => {
  if (!pagination.value.hasMore || isLoading.value) return;

  pagination.value.pageNum++;
  fetchEventList(true);
};

// ==================== 生命周期 ====================
onLoad(() => {
  console.log('活动列表页面加载');
  fetchEventList();

  // 【数据实时更新方案】监听全局数据变化事件
  uni.$on('dataChanged', () => {
    console.log('活动列表页收到数据变化事件，刷新列表...');

    // 重新获取活动列表数据
    fetchEventList();

    uni.showToast({
      title: '列表已更新',
      icon: 'success',
      duration: 1500
    });
  });
});

// 页面显示时隐藏原生 tabBar
onShow(() => {
  uni.hideTabBar();
});

// 页面卸载时移除事件监听
onUnload(() => {
  uni.$off('dataChanged');
});

onReachBottom(() => {
  onLoadMore();
});

onPullDownRefresh(() => {
  onRefresh();
  setTimeout(() => {
    uni.stopPullDownRefresh();
  }, 1000);
});
</script>

<style lang="scss" scoped>
.event-list-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}


.header-wrapper {
  /* 🔧 修改为固定定位，滚动时不移动 */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100; /* 确保头部在最上层 */
  overflow: hidden;
  /* 根据蓝湖设计稿规格设置高度：750rpx × 452rpx，包含状态栏高度 */
  min-height: calc(452rpx + var(--status-bar-height));
  /* 新增：底部圆角，使其与下方白色区域过渡更自然 */
  border-bottom-left-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  padding-bottom: 20rpx;
}

/* 背景图片样式 - 完全覆盖状态栏 */
.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  /* 确保图片按比例缩放，避免变形 */
  object-fit: cover;
  /* 图片居中显示 */
  object-position: center;
}

/* 自定义导航栏样式 */
.custom-navbar {
  position: absolute;
  top: 94rpx;
  left: 0;
  right: 0;
  width: 750rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  z-index: 2;
  border-radius: 0rpx;
}

.navbar-left,
.navbar-right {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-text {
  width: 190rpx;
  height: 44rpx;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
  font-weight: normal;
  font-size: 32rpx;
  color: #FFFFFF;
  line-height: 44rpx;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

/* 确保内容在背景图片之上 */
.top-controls,
.filter-bar {
  position: relative;
  z-index: 2;
}

.top-controls {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  gap: 32rpx;
  position: absolute;
  top: 182rpx;
  left: 16rpx;
  right: 24rpx;
  /* 确保子元素完全水平对齐 */
  height: 60rpx; /* 调整为适应新的搜索框高度 */
}

.search-wrapper {
  width: 446rpx;
  height: 60rpx; /* 调整为与新的搜索框高度一致 */
  flex-shrink: 0; /* 防止被压缩 */
  display: flex;
  align-items: center;
  /* 确保与up-subsection组件基线对齐 */
  vertical-align: middle;
}


.filter-bar {
  position: relative;
  background-color: transparent;
  padding: 0;
  margin-bottom: 10rpx;
}

/* 主筛选按钮容器 */
.filter-main-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24rpx;
}

/* 单个筛选按钮 */
.filter-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 0;
}

.filter-text {
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;
  font-weight: normal;
  font-size: 26rpx;
  color: #66666E;
  line-height: 44rpx;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}


/* 箭头旋转动画 */
.rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}

/* 筛选面板*/
.filter-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  width: 750rpx;
  // height: 446rpx;
  max-height: 60vh;
  overflow-y: auto;
  background: #FFFFFF;
  border-radius: 32rpx 32rpx 32rpx 32rpx;
  border: 2rpx solid #FFFFFF;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 32rpx 24rpx;
  margin: 0 auto;
  max-height: 1046rpx;
  overflow-y: auto;
}

/* 💥 新增：通用的筛选栏吸顶样式 */
.sticky-filter-bar {
  position: fixed;
  /* 头部蓝色区域的高度，可根据实际情况微调 */
  top: calc(260rpx + var(--status-bar-height));
  left: 0;
  right: 0;
  z-index: 102; /* 必须比 scroll-view (101) 更高 */
  background: #FFFFFF;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  padding-top: 10rpx;
  padding-bottom: 10rpx;
}

/* 💥 新增：为带筛选栏的scroll-view添加顶部内边距 */
.list-scroll-with-filter {
  /* 为列表视图的筛选栏预留空间 */
  padding-top: 80rpx !important; /* 根据筛选栏的实际高度调整 */
}

.calendar-scroll-with-filter {
  /* 为日历视图的筛选栏预留空间 */
  padding-top: 80rpx !important; /* 根据筛选栏的实际高度调整 */
}

/* 日历视图专用筛选栏样式 */
.calendar-filter-bar {
  padding: 0 !important; /* 移除默认内边距 */

  /* 日历视图专用按钮容器 */
  .calendar-filter-buttons {
    justify-content: flex-start !important; /* 左对齐 */
    padding: 0 24rpx !important; /* 添加左右内边距 */
    gap: 20rpx !important; /* 设置按钮间距为20rpx */

    .filter-button {
      flex: none !important; /* 取消flex:1，使按钮不等分空间 */
      min-width: auto !important; /* 允许按钮自适应宽度 */
      padding: 16rpx 20rpx !important; /* 保持原有内边距 */
    }
  }
}


/* 筛选分组 */
.filter-section {
  margin-bottom: 40rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

/* 分组标题 */
.section-title {
  width: 104rpx;
  height: 44rpx;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
  font-weight: normal;
  font-size: 26rpx;
  color: #23232A;
  line-height: 44rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 24rpx; /* Keeping this for spacing */
  display: block; /* Keeping this for layout */
}

/* 选项网格 */
.option-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx; /* 进一步减少间距 */
  justify-content: flex-start; /* 改为左对齐，避免挤压 */
}

/* 选项项 - 根据设计规格更新 */
.option-item {
  width: 162rpx; /* 计算后的合适宽度 */
  height: 60rpx;
  background: #F2F4FA; /* 未选中状态的背景色 */
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  border: 2rpx solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  &.active {
    background: rgba(42, 97, 241, 0.2); /* 选中状态：蓝色背景 + 20%透明度 */
    border-color: transparent;

    .option-text {
      color: #023F98; /* 选中状态的文字颜色 */
      font-weight: normal;
    }
  }

  &:hover {
    opacity: 0.8;
  }
}

.option-text {
  width: 112rpx;
  height: 44rpx;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;
  font-weight: normal;
  font-size: 28rpx;
  color: #66666E; /* 未选中状态的文字颜色 */
  line-height: 44rpx;
  text-align: center; /* 修改为居中对齐 */
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 筛选按钮容器 */
.filter-buttons {
  display: flex;
  justify-content: flex-start;
  padding: 32rpx 0 24rpx;
  border-top: 2rpx solid #EEEEEE; /* 添加上边框线 */
  margin-top: 32rpx; /* 增加上边距 */
}

/* 筛选按钮基础样式 */
.filter-btn {
  width: 340rpx; /* 更新宽度为340rpx */
  height: 76rpx;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 24rpx;
  border: none;

  &:last-child {
    margin-right: 0;
  }
}

/* 重置按钮样式 */
.reset-btn {
  background: rgba(42, 97, 241, 0.1); /* 蓝色背景 + 10%透明度 */
}

/* 完成按钮样式 */
.complete-btn {
  background: #023F98; /* 深蓝色背景 */

  &:hover {
    background: #1E4FD9;
  }

  &:active {
    background: #1A43C1;
  }
}

/* 按钮文字基础样式 */
.btn-text {
  width: 56rpx;
  height: 44rpx;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif;
  font-weight: normal;
  font-size: 28rpx;
  line-height: 44rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

/* 重置按钮文字样式 */
.reset-btn .btn-text {
  color: #23232A; /* 深灰色文字 */
}

/* 完成按钮文字样式 */
.complete-btn .btn-text {
  color: #FFFFFF; /* 白色文字 */
}

/* 活动列表滚动区域 - 白色内容容器 (根据蓝湖数据精确设置) */
.event-list-scroll {
  /* 将白色背景、圆角、外边距等样式应用到这里 */
  background-color: #ffffff;
  border-top-left-radius: 20rpx; /* 与下方卡片一致的圆角 */
  border-top-right-radius: 20rpx;
  margin: 0;
  margin-top: calc(452rpx + var(--status-bar-height) - 212rpx);
  position: relative;
  z-index: 101;
  padding-top: 24rpx;

  flex: 1;
  box-sizing: border-box;
  padding-bottom: 180rpx;
  height: calc(100vh - 452rpx - var(--status-bar-height) + 212rpx);
}

/* 日历视图滚动区域  */
.calendar-scroll {
  /* 将白色背景、圆角、外边距等样式应用到这里 */
  background-color: #ffffff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  margin: 0;
  margin-top: -158rpx;
  position: relative;
  padding-top: 24rpx;

  /* 左右内边距，让卡片和筛选栏不要贴边 */
  padding-left: 30rpx;
  padding-right: 30rpx;

  flex: 1;
  box-sizing: border-box;
  padding-bottom: 180rpx; /* 为底部tabBar留空 */
}

.event-card {
  width: 100%;
  height: 272rpx;
  background: #FFFFFF;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  // border: none;
  // border-bottom: 1rpx solid #F5F5F5;
  border: 2rpx solid #EEEEEE;
  margin-bottom: 0rpx;
  padding: 24rpx;
  display: flex;
  overflow: hidden;
  box-sizing: border-box;

  &:last-child {
    border-bottom: none;
  }
}

.card-left {
  position: relative;
  width: 336rpx;
  height: 192rpx;
  flex-shrink: 0;
}

.event-image {
  width: 100%;
  height: 100%;
  display: block;
}

.status-tag {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  width: 90rpx;
  height: 40rpx;
  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;

  /* 内部文字样式 */
  color: #23232A;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
  font-weight: normal;
  font-size: 22rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
  line-height: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &.ended {
    background: linear-gradient(90deg, #909399 0%, #C0C4CC 100%);
  }
}

.card-right {
  flex: 1;
  padding: 16rpx 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.event-title {
  width: 346rpx;
  height: 80rpx;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
  font-weight: normal;
  font-size: 28rpx;
  color: #23232A;
  text-align: justify;
  font-style: normal;
  text-transform: none;
  line-height: 1.4;
  margin-bottom: 24rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.event-info {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.event-info-row {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  gap: 24rpx !important;
  margin-bottom: 18rpx !important;
  flex-wrap: nowrap !important;
}

.time-location-item {
  display: flex !important;
  align-items: center !important;
  gap: 8rpx !important;
  flex-shrink: 0 !important;
}

.event-info-icon {
  width: 32rpx !important;
  height: 32rpx !important;
  flex-shrink: 0 !important;
}

.info-text {
  width: 176rpx !important;
  height: 32rpx !important;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30' !important;
  font-weight: normal !important;
  font-size: 22rpx !important;
  color: #9B9A9A !important;
  text-align: left !important;
  font-style: normal !important;
  text-transform: none !important;
  line-height: 32rpx !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.remaining-spots {
  width: 154rpx;
  height: 40rpx;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  border: 1rpx solid #FB8620;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  overflow: hidden;
  flex-shrink: 0;

  /**
   * 剩余名额文字样式
   * 根据设计要求设置字体和颜色
   */
  .spots-count {
    width: 100%;
    height: 36rpx;
    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
    font-weight: normal;
    font-size: 20rpx;
    color: #FB8620;
    text-align: center;
    font-style: normal;
    text-transform: none;
    line-height: 36rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 空状态样式
.empty-state {
  padding: 100rpx 0;
  text-align: center;
}

// 重试按钮容器
.retry-container {
  margin-top: 40rpx;
  display: flex;
  justify-content: center;
}

// 加载状态优化
.event-list-scroll {
  :deep(.up-loadmore) {
    padding: 30rpx 0;
  }
}

/* 为加载更多组件的包裹容器提供上下内边距 */
.loadmore-wrapper {
  padding-top: 40rpx;
  padding-bottom: 20rpx;
}

// 搜索框优化 - 自定义高度设置
.search-wrapper {
  :deep(.up-search) {
    width: 446rpx;
    height: 40rpx; /* 统一减少搜索框高度 */
    background: #FFFFFF;
    box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2, 63, 152, 0.05);
    border-radius: 20rpx 20rpx 20rpx 20rpx;

    .u-search__content {
      width: 100%;
      height: 100%;
      background: #FFFFFF;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2, 63, 152, 0.05);
      display: flex;
      align-items: center;
    }

    .u-search__content__icon {
      color: #c0c4cc;
    }

    .u-search__content__input {
      color: #303133;
    }
  }
}

// 响应式优化
@media screen and (max-width: 750rpx) {
  .top-controls {
    flex-direction: column;
    gap: 16rpx;

    .search-wrapper {
      width: 446rpx; /* 保持固定宽度 */
      align-self: center; /* 居中显示 */
    }
  }
}

/* up-subsection */
:deep(.u-subsection) {
  width: 224rpx !important;
  height: 60rpx !important;
  background: #FFFFFF !important;
  border-radius: 30rpx 30rpx 30rpx 30rpx !important;
  border: none !important;
  box-shadow: none !important;
  overflow: hidden !important;
  position: relative !important; /* 确保子元素绝对定位的参考点 */
  display: flex !important;
  align-items: center !important;
  /* 确保与搜索框组件基线对齐 */
  vertical-align: middle !important;
}

:deep(.u-subsection__item:not(:first-child)) {
  border-left: none !important;
}

:deep(.u-subsection__item) {
  border: none !important;
  padding: 0 !important;
  background: transparent !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 50% !important; /* 两个item各占50% */
  position: relative !important;
  z-index: 2 !important; /* 确保文字在色块之上 */
}

:deep(.u-subsection__item__text) {
  width: 64rpx !important;
  height: 44rpx !important;
  font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30', sans-serif !important;
  font-weight: normal !important;
  font-size: 32rpx !important;
  color: #23232A !important;
  text-align: center !important;
  font-style: normal !important;
  text-transform: none !important;
  white-space: nowrap !important;
  line-height: 44rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.u-subsection__item:not(.u-subsection__item--active) .u-subsection__item__text) {
  color: rgba(35, 35, 42, 0.6) !important; /* 非激活状态：基础颜色的60%透明度 */
}

:deep(.u-subsection__item--active .u-subsection__item__text) {
  color: #23232A !important; /* 激活状态：使用设计规格的基础颜色 */
  font-weight: normal !important; /* 保持normal字重 */
}

/* 移动色块样式 - 确保对称居中且不被覆盖 */
:deep(.u-subsection__bar) {
  width: 96rpx !important;
  height: 60rpx !important;
  background: linear-gradient(90deg, #FFBF51 0%, #FFDEA1 100%) !important;
  border-radius: 30rpx !important;
  /* 确保居中对称 - 通过margin自动居中 */
  transition: all 0.3s ease !important;
  /* 防止被其他样式覆盖 */
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 1 !important;
  /* 强制覆盖任何可能的样式 */
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
  /* 确保变换不被覆盖 */
  transform-origin: center center !important;
}

// ==================== 💥 日历视图UI设计稿样式规范 (最终版 v2) ====================
.calendar-view {
  /* A. 页面根容器 (Page Root Container) */
  background-color: #FFFFFF; /* 纯白色背景 */
  padding: 0; /* 移除默认内边距 */
  padding-bottom: 180rpx; /* 为底部tabBar留出空间 */
  box-sizing: border-box;

  .vertical-timeline {
    /* B. 内容渐变背景容器 (Content Gradient Background Container) */
    width: 702rpx;
    background: linear-gradient(181deg, #EDF2FF 0%, #FFFFFF 100%);
    border-radius: 32rpx;
    margin: 0 24rpx; /* 居中对齐，左右各24rpx */
    padding: 20rpx 0; /* 上下内边距 */
    box-sizing: border-box;
    position: relative;
  }

  .date-section {
    position: relative;
    padding-left: 72rpx;

    &:not(:last-child) {
      margin-bottom: 24rpx;
    }
  }

  .date-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 32rpx; /* 增加与卡片的间距 */

    /* 中心圆点 */
    .timeline-dot {
      position: absolute;
      left: 24rpx; /* 距离容器左边缘24rpx */
      top: 13rpx; /* 与日期文字垂直居中 */
      width: 18rpx;
      height: 18rpx;
      background: #FFFFFF;
      border: 2rpx solid #023F98;
      border-radius: 50%;
      z-index: 2;
    }

    /* 日期文字 (例如 "08.23") */
    .time-text {
      font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
      font-size: 32rpx;
      color: #023F98;
      line-height: 44rpx;
      font-weight: normal;
    }

    /* 星期文字 (例如 "周二") */
    .weekday-text {
      font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
      font-size: 22rpx;
      color: #66666E;
      line-height: 44rpx;
      font-weight: normal;
      margin-left: 15rpx;
    }

  }

  /* 垂直连接线 */
  .line-connector {
    position: absolute;
    left: 32rpx; /* 圆点中心位置 24rpx + 9rpx(圆点半径) - 1rpx(线宽一半) */
    top: 44rpx; /* 从日期文字下方开始 */
    bottom: -197rpx; /* 延伸到下一个日期分组 */
    width: 2rpx;

    .timeline-line {
      height: 100%;
      width: 100%;
      background: #023F98;
    }
  }

  /* 最后一个日期分组不显示连接线 */
  .date-section:last-child .line-connector .timeline-line {
    display: none;
  }

  /* 💥 最终修正：使用伪元素创建无缝连接的尖角 */
  .vertical-timeline::before {
    content: '';
    position: absolute;
    top: -20rpx; /* 向上移动一半的高度 */
    left: 55rpx; /* 从左侧定位，请根据您的界面微调此值 */
    width: 40rpx;
    height: 40rpx;

    /* 关键：使用与渐变顶部完全相同的纯色 */
    background: #EDF2FF;

    transform: rotate(45deg);

    /* 关键：给尖角一点点圆角，可以极大地优化边缘的锯齿感 */
    border-top-left-radius: 4rpx;
  }

  .events-container {
    .compact-event-card {
      /* 活动卡片容器 */
      width: 590rpx;
      height: 100rpx;
      background: #FFFFFF;
      border: 2rpx solid #023F98;
      border-radius: 16rpx;
      padding: 0 24rpx; /* 修改内边距为左右24rpx，上下由flex控制 */
      margin-bottom: 20rpx;
      display: flex;
      align-items: center;
      gap: 12rpx; /* 标题与Logo间距 */
      transition: transform 0.2s ease;
      box-sizing: border-box;

      &:last-child {
        margin-bottom: 0;
      }

      &:active {
        transform: scale(0.98);
      }

      /* 左侧Logo */
      .event-avatar {
        width: 52rpx;
        height: 52rpx;
        border-radius: 8rpx;
        flex-shrink: 0;
        background-color: #f5f5f5;
      }

      .event-content {
        flex: 1;
        min-width: 0;
        display: flex; /* 关键：改为水平flex布局 */
        align-items: center; /* 垂直居中 */
        justify-content: space-between; /* 从左侧开始排列 */

        /* 活动标题 */
        .event-title-compact {
          font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
          font-size: 28rpx;
          color: #23232A;
          font-weight: normal;
          min-width: 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 1.2;
        }

        .location-group {
          display: flex;
          align-items: center;
          flex-shrink: 0; /* 防止被压缩 */
        }

        /* 💥 新增：分割线样式 */
        .separator-line {
          width: 2rpx;
          height: 40rpx;
          background: #FA841C;
          opacity: 0.3;
          flex-shrink: 0; /* 防止被压缩 */
          margin-right: 24rpx;
        }

        /* 地点信息 */
        .event-location-compact {
          display: flex;
          flex-direction: column; /* 垂直排列图标和文字 */
          align-items: center; /* 水平居中对齐 */
          justify-content: center; /* 垂直居中对齐 */
          flex-shrink: 0; /* 防止被压缩 */
          gap: 4rpx; /* 图标与文字的间距，减小垂直间距 */

          /* 定位图标 */
          .location-icon {
            width: 32rpx;
            height: 32rpx;
            flex-shrink: 0;
          }

          /* 地点文字 */
          .location-text {
            font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
            font-size: 22rpx;
            color: #452D03; /* 应用新的文字颜色 */
            font-weight: normal;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.2;
          }
        }
      }
    }
  }

  /* 查看更多活动按钮 */
  .view-more-button {
    width: 628rpx;
    height: 88rpx;
    background: #023F98;
    border-radius: 16rpx;
    margin: 60rpx auto 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background: #1E4FD9;
    }

    &:active {
      background: #1A43C1;
    }

    .button-text {
      font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
      font-size: 32rpx;
      color: #FFFFFF;
      font-weight: normal;
      text-align: center;
    }
  }
}

/* ==================== 💥 搜索框强制样式覆盖 (解决父容器样式限制) ==================== */
/* 解决搜索框样式被.top-controls和up-subsection影响的问题 */
.search-wrapper {
  /* 强制覆盖父容器的样式限制 */
  height: 60rpx !important; /* 更新为新的高度规格 */
  display: flex !important;
  align-items: center !important;
  flex-shrink: 0 !important;
}

/* 针对CustomSearchBox组件内部的u-search样式强制覆盖 */
.search-wrapper :deep(.u-search) {
  height: 60rpx !important; /* 应用新的高度 */
  width: 446rpx !important; /* 确保宽度正确 */
  border: none !important;
}

.search-wrapper :deep(.u-search__content) {
  height: 60rpx !important; /* 应用新的高度 */
  padding: 0 20rpx !important; /* 调整内边距 */
  background: #FFFFFF !important; /* 新的白色背景 */
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(2, 63, 152, 0.05) !important; /* 新的阴影效果 */
  border-radius: 30rpx !important; /* 新的圆角规格 */
}

.search-wrapper :deep(.u-search__input-wrap) {
  height: 60rpx !important; /* 应用新的高度 */
  padding: 0 !important;
}

.search-wrapper :deep(.u-search__input) {
  height: 60rpx !important; /* 应用新的高度 */
  line-height: 60rpx !important;
  font-size: 28rpx !important; /* 调整字体大小 */
  color: #333333 !important; /* 深色文字适配白色背景 */
  background-color: transparent !important;
}

.search-wrapper :deep(.u-search__input::placeholder) {
  color: #999999 !important; /* 调整占位符颜色适配白色背景 */
  font-size: 28rpx !important; /* 调整字体大小 */
}

.search-wrapper :deep(.u-search__action) {
  height: 60rpx !important; /* 应用新的高度 */
  padding: 0 8rpx !important;
}

.search-wrapper :deep(.u-search__action-text) {
  font-size: 28rpx !important; /* 调整字体大小 */
  color: #333333 !important; /* 深色文字适配白色背景 */
}

</style>