<template>
  <view class="page-container">
    <scroll-view class="main-scroll-view" scroll-y>
      <HeaderComponent />

      <view>
        <BannerComponent />
      </view>

      <view>
        <QuickNavigationComponent/>
      </view>

      <view>
        <CountryHighlightComponent/>
      </view>
	  
	  <view>
	    <EventPromotionComponent/>
	  </view> 

      <view>
        <NewsListComponent/>
      </view>

    </scroll-view>

    <CustomTabBar :current="0"/>

    <view class="fab-customer-service" @click="navigateToService">
      <image class="fab-icon" src="/static/icons/客服 <EMAIL>" mode="aspectFit"></image>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import { getAdListByPositionApi } from '@/api/platform/ad.js';
import {HeaderComponent} from "@/components/home/<USER>";
import {BannerComponent} from '@/components/home/<USER>';
import {QuickNavigationComponent} from '@/components/home/<USER>';
import {CountryHighlightComponent} from '@/components/home/<USER>';
import {NewsListComponent} from "@/components/home/<USER>";
// import ActivityGridComponent from '@/components/home/<USER>';
import EventPromotionComponent from '@/components/home/<USER>';
import PopupAdComponent from '@/components/common/PopupAdComponent.vue';
import CustomTabBar from '@/components/layout/CustomTabBar.vue';

// --- 弹窗广告相关逻辑 ---
const showPopupAd = ref(false);
const popupAdData = ref({});
const AD_POSITION_CODE = 'splash_screen';
const POPUP_STORAGE_KEY = 'splashPopupShownDate';

const checkAndShowPopupAd = async () => {
  try {
    const response = await getAdListByPositionApi(AD_POSITION_CODE, {
      pageSize: 1
    });
    if (response && response.data && response.data.length > 0) {
      popupAdData.value = response.data[0];
      showPopupAd.value = true;
    }
  } catch (error) {
    console.error('获取弹窗广告失败:', error.message || error);
  }
};

const navigateToService = () => {
  uni.navigateTo({
    url: '/pages_sub/pages_profile/contact'
  });
};

onShow(() => {
  uni.hideTabBar();
  checkAndShowPopupAd();
});
</script>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #FFFFFF;
  overflow: hidden;
}

.main-scroll-view {
  flex: 1;
  height: 0;

  /* --- 新增修改：开始 --- */
  /* 1. 创建定位上下文，以使 z-index 生效 */
  position: relative;
  /* 2. 设置一个较低的层级，确保它在 HeaderComponent (z-index: 999) 之下 */
  z-index: 0;
  /* 3. 强制创建新的渲染层，解决部分平台下z-index失效的疑难杂症 */
  transform: translateZ(0);
  /* --- 新增修改：结束 --- */

  padding: 0 24rpx;
  padding-top: 176rpx;
  padding-bottom: calc(144rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

.fab-customer-service {
  position: fixed;
  right: 20rpx;
  bottom: calc(200rpx + env(safe-area-inset-bottom));
  width: 100rpx;
  height: 100rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  transition: opacity 0.3s;
}

.fab-customer-service:active {
  opacity: 0.7;
}

.fab-icon {
  width: 60rpx;
  height: 60rpx;
}
</style>