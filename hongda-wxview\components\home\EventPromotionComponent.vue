<template>
	<view class="promo-container">
		<!-- 调试信息 - 开发时使用 -->
	<!-- 	<view v-if="true" class="debug-info" style="padding: 10rpx; background: #f0f0f0; margin-bottom: 10rpx; font-size: 24rpx;">
			<text>isLoading: {{ isLoading }}</text><br/>
			<text>promoDataList.length: {{ promoDataList.length }}</text><br/>
			<text>currentIndex: {{ currentIndex }}</text>
		</view> -->
		
		<!-- 骨架屏 - 加载时显示 -->
		<view v-if="isLoading" class="skeleton-wrapper">
			<u-skeleton
				:loading="true"
				:animate="true"
				:rows="4"
				:title="true"
				titleWidth="60%"
				rowsWidth="['100%', '40%', '40%', '100%']"
				rowsHeight="['180px', '20px', '20px', '40px']"
			></u-skeleton>
		</view>

		<!-- 轮播卡片容器 - 有数据时显示 -->
		<view v-else-if="promoDataList.length > 0" class="promo-card-wrapper">
			<!-- 轮播区域 -->
			<swiper 
				ref="swiperRef"
				class="promo-swiper"
				:indicator-dots="false"
				:autoplay="true"
				:interval="4000"
				:duration="500"
				:circular="true"
				:current="currentIndex"
				@change="onSwiperChange"
			>
				<swiper-item 
					v-for="(item, index) in promoDataList" 
					:key="item.id || index"
					class="swiper-item"
				>
					<view class="promo-card" @click="handlePromoClick(item)">
						<!-- 1. 顶部标题 -->
						<view class="promo-header">
							<image v-if="item.iconUrl" :src="getFullImageUrl(item.iconUrl)" class="promo-icon" mode="aspectFill"></image>
							<text class="promo-title">{{ item.title }}</text>
						</view>

						<!-- 2. 中间主图 -->
						<image 
							class="promo-main-image" 
							:src="item.image" 
							mode="aspectFill"
							:lazy-load="true"
							@error="onImageError"
							@load="onImageLoad"
						></image>
						
						<!-- 3. 简介信息 -->
						<view class="promo-description">
							<view v-if="item.descriptionLine1" class="desc-item">
								<image class="desc-icon" src="/static/EventPromotion/flame-icon.png"></image>
								<text class="desc-text">{{ item.descriptionLine1 }}</text>
							</view>
							<view v-if="item.descriptionLine2" class="desc-item">
								<image class="desc-icon" src="/static/EventPromotion/thumb-up-icon.png"></image>
								<text class="desc-text">{{ item.descriptionLine2 }}</text>
							</view>
						</view>
						
						<!-- 4. 底部按钮 -->
						<view class="promo-footer">
							<up-button 
								type="primary" 
								shape="square" 
								text="立即报名"
								size="large"
								:customStyle="{ 
									backgroundColor: '#0052D9', /*蓝色 */
									height: '70rpx'
									}"
							></up-button>
						</view>
					</view>
				</swiper-item>
			</swiper>
			
			<!-- 自定义指示器 - 放在立即报名按钮下方 -->
			<view v-if="promoDataList.length > 1" class="custom-indicators">
				<view 
					v-for="(item, index) in promoDataList" 
					:key="index"
					class="indicator-dot"
					:class="{ 'active': currentIndex === index }"
					@click="switchToSlide(index)"
				></view>
			</view>
		</view>
		
		<!-- 无数据时显示提示 -->
		<view v-else class="no-data-tip" style="padding: 40rpx; text-align: center; color: #999;">
			<text>暂无推广活动</text>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getAdListByPositionApi } from '@/api/platform/ad.js';
import { getFullImageUrl } from '@/utils/image.js';

// --- 响应式状态 ---
// 改为数组，支持多个推广数据
const promoDataList = ref([]); 
const isLoading = ref(true);
const currentIndex = ref(0); // 当前轮播索引

// 广告位代码常量
const POSITION_CODE = 'HOME_PROMO_EVENT';

// --- 方法 ---

/**
 * 获取推广活动数据
 */
const fetchPromoData = async () => {
	try {
		// 获取多条数据支持轮播
		const response = await getAdListByPositionApi(POSITION_CODE, {
			pageSize: 10
		});
		
		console.log('活动推广API响应:', response);
		
		if (response.data && Array.isArray(response.data) && response.data.length > 0) {
			// 处理所有返回的广告数据
			promoDataList.value = response.data.map(ad => {
				
				const processedData = {
					id: ad.id,
					title: ad.title,
					image: getFullImageUrl(ad.imageUrl),
					iconUrl: ad.iconUrl, // 添加图标URL
					linkUrl: ad.finalLinkUrl || ad.linkUrl, // 优先使用最终跳转链接
					// 使用后端返回的活动简介和卖点字段，如果为空则提供默认文案
					descriptionLine1: ad.eventSummary || '官方认证，品质保证',
					descriptionLine2: ad.eventSellPoint || '干货满满，不容错过'
				};
				return processedData;
			});
		} else {
			console.warn('未找到推广数据或返回格式异常:', response);
			promoDataList.value = []; // 确保清空数据
		}
		
	} catch (error) {
		console.error('获取推广数据失败:', error);
	} finally {
		isLoading.value = false;
	}
};

/**
 * 轮播切换事件处理
 */
const onSwiperChange = (e) => {
	currentIndex.value = e.detail.current;
	// console.log('轮播切换到索引:', currentIndex.value);
};

/**
 * 点击指示器切换轮播
 */
const switchToSlide = (index) => {
	currentIndex.value = index;
	console.log('点击指示器切换到索引:', index);
};

/**
 * 处理卡片点击事件
 */
const handlePromoClick = (promoItem) => {
	if (promoItem && promoItem.linkUrl) {
		const linkUrl = promoItem.linkUrl;
		console.log('准备跳转到:', linkUrl);
		
		if (linkUrl.startsWith('http')) {
			uni.navigateTo({
				url: `/pages/webview/index?url=${encodeURIComponent(linkUrl)}&title=${encodeURIComponent(promoItem.title || '详情')}`
			});
		} else {
			uni.navigateTo({
				url: linkUrl
			});
		}
	} else {
		console.warn('推广卡片跳转链接为空');
	}
};

/**
 * 图片加载成功事件
 */
const onImageLoad = (e) => {
	console.log('图片加载成功');
};

/**
 * 图片加载失败事件
 */
const onImageError = (e) => {
	console.error('图片加载失败:', e);
};

// --- 生命周期 ---
onMounted(() => {
	fetchPromoData();
});
</script>

<style lang="scss" scoped>
.promo-container {
	width: 702rpx;
	height: 624rpx;
	background: linear-gradient(180deg, rgba(2,63,152,0.1) 0%, rgba(2,63,152,0) 100%);
	border-radius: 32rpx 32rpx 32rpx 32rpx;
	margin: 24rpx;
}

.skeleton-wrapper {
	padding: 24rpx;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	height: 100%;
}

// 轮播卡片容器
.promo-card-wrapper {
	border-radius: 16rpx;
	// overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	height: 100%;
}

// 轮播组件样式
.promo-swiper {
	width: 100%;
	height: 500rpx; /* 设置固定高度，确保swiper正常显示 */
}

.swiper-item {
	width: 100%;
	height: 100%; /* 占满swiper容器 */
}

.promo-card {
	padding: 24rpx;
	width: 100%;
	height: 100%; /* 占满swiper-item */
	box-sizing: border-box;
	display: flex;
	flex-direction: column; /* 垂直布局 */
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.promo-header {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	flex-shrink: 0; /* 防止被压缩 */
	
	.promo-icon {
		width: 44rpx;
		height: 44rpx;
		border-radius: 8rpx;
		margin-right: 12rpx;
		flex-shrink: 0;
	}
	
	.promo-title {
		width: 552rpx;
		height: 44rpx;
		font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;
		font-weight: normal;
		font-size: 32rpx;
		color: #23232A;
		text-align: left;
		font-style: normal;
		text-transform: none;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		flex: 1;
		line-height: 44rpx;
	}
}

.promo-main-image {
	width: 100%;
	max-height: 200rpx; /* 限制图片最大高度 */
	border-radius: 12rpx;
	display: block;
	object-fit: cover; /* 保持图片比例 */
	flex-shrink: 0; /* 防止被压缩 */
}

.promo-description {
	margin: 24rpx 0;
	flex: 1; /* 占据剩余空间 */
	
	.desc-item {
		display: flex;
		align-items: flex-start; /* 图标顶部对齐 */
		margin-bottom: 16rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
	}
	
	/* [新增] 自定义图标样式 */
	.desc-icon {
	    width: 36rpx;
	    height: 36rpx;
	    /* 防止图标在flex布局中被压缩变形 */
	    flex-shrink: 0; 
	}
	
	.desc-text {
		font-size: 28rpx;
		color: #606266;
		margin-left: 12rpx;
		line-height: 1.4;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2; /* 最多显示2行 */
		overflow: hidden;
	}
}

.promo-footer {
	flex-shrink: 0; /* 防止被压缩 */
	margin-top: auto; /* 推到底部 */
}

// 自定义指示器样式 - 位于立即报名按钮下方
.custom-indicators {
	display: flex;
		justify-content: center;
		align-items: center;
		padding: 24rpx 0 16rpx 0;
		gap: 12rpx;
}

.indicator-dot {
	width: 24rpx; /* 默认就是长条宽度 */
		height: 12rpx;
		border-radius: 6rpx; /* 默认就是胶囊圆角 */
	background-color: #e4e7ed; /* 未激活状态的浅灰色 */
	transition: background-color 0.3s ease;
	
	/* 激活状态 */
	&.active {
		background-color: #004085; /* [颜色修改] 更深的蓝色 */
	}
}
</style>
