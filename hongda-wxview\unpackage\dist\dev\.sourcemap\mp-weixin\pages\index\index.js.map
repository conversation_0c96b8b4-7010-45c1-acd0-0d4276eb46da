{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"page-container\">\r\n    <scroll-view class=\"main-scroll-view\" scroll-y>\r\n      <HeaderComponent />\r\n\r\n      <view>\r\n        <BannerComponent />\r\n      </view>\r\n\r\n      <view>\r\n        <QuickNavigationComponent/>\r\n      </view>\r\n\r\n      <view>\r\n        <CountryHighlightComponent/>\r\n      </view>\r\n\t  \r\n\t  <view>\r\n\t    <EventPromotionComponent/>\r\n\t  </view> \r\n\r\n      <view>\r\n        <NewsListComponent/>\r\n      </view>\r\n\r\n    </scroll-view>\r\n\r\n    <CustomTabBar :current=\"0\"/>\r\n\r\n    <view class=\"fab-customer-service\" @click=\"navigateToService\">\r\n      <image class=\"fab-icon\" src=\"/static/icons/客服 <EMAIL>\" mode=\"aspectFit\"></image>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue';\r\nimport { onShow } from '@dcloudio/uni-app';\r\nimport { getAdListByPositionApi } from '@/api/platform/ad.js';\r\nimport {HeaderComponent} from \"@/components/home/<USER>\";\r\nimport {BannerComponent} from '@/components/home/<USER>';\r\nimport {QuickNavigationComponent} from '@/components/home/<USER>';\r\nimport {CountryHighlightComponent} from '@/components/home/<USER>';\r\nimport {NewsListComponent} from \"@/components/home/<USER>\";\r\n// import ActivityGridComponent from '@/components/home/<USER>';\r\nimport EventPromotionComponent from '@/components/home/<USER>';\r\nimport PopupAdComponent from '@/components/common/PopupAdComponent.vue';\r\nimport CustomTabBar from '@/components/layout/CustomTabBar.vue';\r\n\r\n// --- 弹窗广告相关逻辑 ---\r\nconst showPopupAd = ref(false);\r\nconst popupAdData = ref({});\r\nconst AD_POSITION_CODE = 'splash_screen';\r\nconst POPUP_STORAGE_KEY = 'splashPopupShownDate';\r\n\r\nconst checkAndShowPopupAd = async () => {\r\n  try {\r\n    const response = await getAdListByPositionApi(AD_POSITION_CODE, {\r\n      pageSize: 1\r\n    });\r\n    if (response && response.data && response.data.length > 0) {\r\n      popupAdData.value = response.data[0];\r\n      showPopupAd.value = true;\r\n    }\r\n  } catch (error) {\r\n    console.error('获取弹窗广告失败:', error.message || error);\r\n  }\r\n};\r\n\r\nconst navigateToService = () => {\r\n  uni.navigateTo({\r\n    url: '/pages_sub/pages_profile/contact'\r\n  });\r\n};\r\n\r\nonShow(() => {\r\n  uni.hideTabBar();\r\n  checkAndShowPopupAd();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #FFFFFF;\r\n  overflow: hidden;\r\n}\r\n\r\n.main-scroll-view {\r\n  flex: 1;\r\n  height: 0;\r\n\r\n  /* --- 新增修改：开始 --- */\r\n  /* 1. 创建定位上下文，以使 z-index 生效 */\r\n  position: relative;\r\n  /* 2. 设置一个较低的层级，确保它在 HeaderComponent (z-index: 999) 之下 */\r\n  z-index: 0;\r\n  /* 3. 强制创建新的渲染层，解决部分平台下z-index失效的疑难杂症 */\r\n  transform: translateZ(0);\r\n  /* --- 新增修改：结束 --- */\r\n\r\n  padding: 0 24rpx;\r\n  padding-top: 176rpx;\r\n  padding-bottom: calc(144rpx + env(safe-area-inset-bottom));\r\n  box-sizing: border-box;\r\n}\r\n\r\n.fab-customer-service {\r\n  position: fixed;\r\n  right: 20rpx;\r\n  bottom: calc(200rpx + env(safe-area-inset-bottom));\r\n  width: 100rpx;\r\n  height: 100rpx;\r\n  background-color: #FFFFFF;\r\n  border-radius: 50%;\r\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 999;\r\n  transition: opacity 0.3s;\r\n}\r\n\r\n.fab-customer-service:active {\r\n  opacity: 0.7;\r\n}\r\n\r\n.fab-icon {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "getAdListByPositionApi", "uni", "onShow"], "mappings": ";;;;;;;AAuCA,MAAM,kBAAkB,MAAW;AACnC,MAAM,kBAAkB,MAAW;AACnC,MAAM,2BAA2B,MAAW;AAC5C,MAAM,4BAA4B,MAAW;AAC7C,MAAM,oBAAoB,MAAW;AAErC,MAAM,0BAA0B,MAAW;AAE3C,MAAM,eAAe,MAAW;AAKhC,MAAM,mBAAmB;;;;AAFzB,UAAM,cAAcA,cAAAA,IAAI,KAAK;AAC7B,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAI1B,UAAM,sBAAsB,YAAY;AACtC,UAAI;AACF,cAAM,WAAW,MAAMC,gBAAsB,uBAAC,kBAAkB;AAAA,UAC9D,UAAU;AAAA,QAChB,CAAK;AACD,YAAI,YAAY,SAAS,QAAQ,SAAS,KAAK,SAAS,GAAG;AACzD,sBAAY,QAAQ,SAAS,KAAK,CAAC;AACnC,sBAAY,QAAQ;AAAA,QACrB;AAAA,MACF,SAAQ,OAAO;AACdC,0EAAc,aAAa,MAAM,WAAW,KAAK;AAAA,MAClD;AAAA,IACH;AAEA,UAAM,oBAAoB,MAAM;AAC9BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAEAC,kBAAAA,OAAO,MAAM;AACXD,oBAAG,MAAC,WAAU;AACd;IACF,CAAC;;;;;;;;;;;;;AC7ED,GAAG,WAAW,eAAe;"}