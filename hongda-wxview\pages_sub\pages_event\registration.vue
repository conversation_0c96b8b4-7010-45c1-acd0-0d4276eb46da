<template>
  <view class="registration-page">
    <!-- 自定义导航栏 -->
    <up-navbar
      :title="eventInfo?.title || '活动报名'"
      :fixed="true"
      :safeAreaInsetTop="true"
      bgColor="transparent"
      leftIcon="arrow-left"
      leftIconColor="#333333"
      titleStyle="color: #333333; font-weight: bold;"
      @leftClick="handleNavBack"
    />

    <!-- 全局加载状态 -->
    <view class="loading-container" v-if="isLoading">
      <up-loading-page loadingText="正在加载报名表单..." loadingMode="spinner"></up-loading-page>
    </view>

    <!-- 未登录状态提示 -->
    <view class="login-prompt-container" v-else-if="!isLoggedIn">
      <up-empty 
        mode="permission" 
        text="请先登录后进行报名"
        textColor="#909399"
        textSize="28"
      >
        <template #button>
          <up-button 
            type="primary" 
            text="立即登录"
            @click="goToLogin"
            customStyle="background-color: #f56c6c; border-color: #f56c6c; width: 200rpx; height: 70rpx; font-size: 28rpx;"
          ></up-button>
        </template>
      </up-empty>
    </view>

    <!-- 空状态：无表单配置或加载失败 -->
    <view class="empty-container" v-else-if="isLoggedIn && (!formConfig || formConfig.length === 0)">
      <up-empty 
        mode="data" 
        text="该活动无需报名表单"
        textColor="#909399"
        textSize="28"
      ></up-empty>
    </view>

    <!-- 主要内容区域：动态表单 (只有登录用户才能看到) -->
    <scroll-view scroll-y class="scroll-content" v-else-if="isLoggedIn && formConfig && formConfig.length > 0">
      
      <!-- 活动名称显示区域 -->
      <view class="event-title-section" v-if="eventInfo">
        <text class="event-title">{{ eventInfo.title }}</text>
      </view>

      <!-- 动态表单 -->
      <view class="form-container">
        <up-form
          :model="formData"
          :rules="formRules"
          ref="formRef"
          labelPosition="top"
          labelWidth="auto"
          :labelStyle="{
             fontFamily: 'Alibaba PuHuiTi 3.0-55 Regular',
                 fontWeight: 'normal',
                 fontSize: '28rpx',
                 color: '#23232A',
                 lineHeight: 'normal'
            }"
        >
          <!-- 动态渲染表单项 -->
          <template v-for="(item, index) in formConfig" :key="index">
            
            <!-- 单行输入框 -->
            <up-form-item 
              v-if="item.type === 'input'"
              :label="item.label"
              :prop="item.field"
              :required="item.required"
              class="form-item"
              :style="{ marginBottom: '92rpx' }"
            >
              <up-input
                v-model="formData[item.field]"
                :placeholder="item.props?.placeholder || '请输入'"
                :clearable="true"
                :maxlength="item.props?.maxlength || 100"
                customStyle="width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;"
              ></up-input>
            </up-form-item>

            <!-- 多行文本输入 -->
            <up-form-item 
              v-else-if="item.type === 'textarea'"
              :label="item.label"
              :prop="item.field"
              :required="item.required"
              class="form-item"
              :style="{ marginBottom: '92rpx' }"
            >
              <up-textarea
                v-model="formData[item.field]"
                :placeholder="item.props?.placeholder || '请输入'"
                :maxlength="item.props?.maxlength || 500"
                height="120"
                count
                customStyle="width: 686rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;"
              ></up-textarea>
            </up-form-item>

            <!-- 下拉选择框 -->
            <up-form-item 
              v-else-if="item.type === 'select'"
              :label="item.label"
              :prop="item.field"
              :required="item.required"
              class="form-item"
              :style="{ marginBottom: '92rpx' }"
            >
              <up-input
                :value="getSelectDisplayValue(item.field, item.options)"
                :placeholder="item.props?.placeholder || '请选择'"
                readonly
                suffixIcon="arrow-down-fill"
                @click="openPicker(item)"
                customStyle="width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;"
              ></up-input>
            </up-form-item>

            <!-- 数字输入框 -->
            <up-form-item 
              v-else-if="item.type === 'number'"
              :label="item.label"
              :prop="item.field"
              :required="item.required"
              class="form-item"
              :style="{ marginBottom: '92rpx' }"
            >
              <up-input
                v-model="formData[item.field]"
                :placeholder="item.props?.placeholder || '请输入数字'"
                type="number"
                :clearable="true"
                customStyle="width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;"
              ></up-input>
            </up-form-item>

            <!-- 手机号输入框 -->
            <up-form-item 
              v-else-if="item.type === 'phone'"
              :label="item.label"
              :prop="item.field"
              :required="item.required"
              class="form-item"
              :style="{ marginBottom: '92rpx' }"
            >
              <up-input
                v-model="formData[item.field]"
                :placeholder="item.props?.placeholder || '请输入手机号'"
                type="number"
                :clearable="true"
                :maxlength="11"
                customStyle="width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;"
              ></up-input>
            </up-form-item>

            <!-- 邮箱输入框 -->
            <up-form-item 
              v-else-if="item.type === 'email'"
              :label="item.label"
              :prop="item.field"
              :required="item.required"
              class="form-item"
              :style="{ marginBottom: '92rpx' }"
            >
              <up-input
                v-model="formData[item.field]"
                :placeholder="item.props?.placeholder || '请输入邮箱地址'"
                :clearable="true"
                customStyle="width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;"
              ></up-input>
            </up-form-item>

          </template>
        </up-form>
      </view>

      <!-- 底部留白，为固定按钮腾出空间 -->
      <view class="bottom-spacer"></view>
    </scroll-view>

    <!-- 底部提交按钮 (只有登录用户且有表单时才显示) -->
    <view class="bottom-action-bar" v-if="isLoggedIn && !isLoading && formConfig && formConfig.length > 0">
      <up-button 
        type="primary"
        shape="circle"
        size="large"
        :loading="isSubmitting"
        :disabled="isSubmitting"
        @click="handleSubmit"
        customStyle="width: 702rpx; height: 76rpx; background: #023F98; border-radius: 8rpx; border-color: #023F98; font-size: 28rpx;"
      >
        {{ isSubmitting ? '提交中...' : '提交报名信息' }}
      </up-button>
    </view>

    <!-- 选择器弹窗 -->
    <up-picker
      ref="pickerRef"
      :show="pickerShow"
      :columns="pickerColumns"
      @confirm="onPickerConfirm"
      @cancel="pickerShow = false"
      keyName="label"
    ></up-picker>

    <!-- 报名确认弹窗 -->
    <view v-if="showConfirmModal" class="confirm-modal-overlay" @click="closeConfirmModal">
      <view class="confirm-modal-content" @click.stop>
        <!-- 警告图标和标题 -->
        <view class="modal-header">
          <image class="warning-icon" src="/static/profile/orders_warning.png" mode="aspectFit"></image>
          <text class="modal-title">操作提示</text>
        </view>

        <!-- 提示内容 -->
        <view class="modal-body">
          <text class="modal-message">确认提交报名信息？</text>
        </view>

        <!-- 按钮组 -->
        <view class="modal-footer">
          <view class="modal-btn cancel-btn" @click="closeConfirmModal">
            暂不提交
          </view>
          <view class="modal-btn confirm-btn" @click="confirmSubmitRegistration">
            确认提交
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import {reactive, ref} from 'vue';
import {onLoad, onShow} from '@dcloudio/uni-app';
import {getFormDefinitionApi, submitRegistrationApi} from '@/pages_sub/api/data/registration.js';
import {getEventDetailApi} from '@/api/data/event.js';

// 响应式数据
const eventId = ref(null);
const eventInfo = ref(null); // 新增：活动信息
const formConfig = ref([]);
const formData = reactive({});
const formRules = reactive({});
const isLoading = ref(true);
const isSubmitting = ref(false);
const isLoggedIn = ref(false); // 新增：登录状态管理

// 选择器相关
const pickerShow = ref(false);
const pickerColumns = ref([]);
const currentPickerField = ref('');

// 确认弹窗相关
const showConfirmModal = ref(false);

// 表单引用
const formRef = ref();
const pickerRef = ref();

// 检查登录状态的函数
const checkLoginStatus = () => {
  const token = uni.getStorageSync('token');
  return !!token; // 转换为布尔值
};

// 页面加载 - 只处理基础参数，不进行登录检查
onLoad(async (options) => {
  eventId.value = options.id;
  
  if (!eventId.value) {
    uni.$u.toast('活动信息错误');
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
    return;
  }
  
  // 注意：这里不再调用 loadFormDefinition()，移动到 onShow 中
});

// 页面显示时的逻辑 - 核心登录检查逻辑
onShow(async () => {
  console.log('=== 报名页面 onShow 触发 ===');
  
  // 每次页面显示时都重新检查登录状态
  const currentLoginStatus = checkLoginStatus();
  isLoggedIn.value = currentLoginStatus;
  
  console.log('当前登录状态:', currentLoginStatus);
  console.log('当前token:', uni.getStorageSync('token'));
  
  if (!currentLoginStatus) {
    // 用户未登录，立即返回上一页（detail页面）
    console.log('用户未登录，返回上一页让detail页面处理登录跳转');
    isLoading.value = false; // 停止加载状态
    
    uni.showToast({
      title: '请先登录后报名',
      icon: 'none',
      duration: 1500
    });
    
    // 立即返回上一页，避免在registration页面进行登录跳转
    setTimeout(() => {
      console.log('返回上一页（detail页面）');
      uni.navigateBack({
        fail: () => {
          // 如果返回失败，说明可能是直接访问的registration页面
          console.log('返回失败，跳转到首页');
          uni.switchTab({ url: '/pages/index/index' });
        }
      });
    }, 800);
    
  } else {
    // 用户已登录，可以正常进行报名操作
    console.log('用户已登录，开始加载活动信息和表单数据');
    
    // 加载活动信息（无论是否已加载过都重新加载，确保信息是最新的）
    if (eventId.value) {
      await loadEventInfo();
    }
    
    // 只有在已登录的情况下才加载表单
    if (eventId.value && (!formConfig.value || formConfig.value.length === 0)) {
      await loadFormDefinition();
    } else {
      // 如果表单已经加载过，直接停止loading状态
      isLoading.value = false;
    }
  }
});

// 跳转到登录页的方法
const goToLogin = () => {
  console.log('手动跳转到登录页');
  uni.navigateTo({
    url: '/pages/login/index'
  });
};

// 智能导航回退处理函数
const handleNavBack = () => {
  console.log('=== 开始智能导航回退处理 ===');
  
  // 第一步：尝试正常回退
  uni.navigateBack({
    success: () => {
      console.log('✅ 正常回退成功');
    },
    fail: (err) => {
      console.warn('⚠️ 正常回退失败:', err);
      
      // 第二步：尝试跳转到活动列表页面
      uni.navigateTo({
        url: '/pages/event/index',
        success: () => {
          console.log('✅ 跳转到活动列表页面成功');
        },
        fail: (err2) => {
          console.warn('⚠️ 跳转到活动列表页面失败:', err2);
          
          // 第三步：最后的兜底方案，跳转到首页
          uni.switchTab({
            url: '/pages/index/index',
            success: () => {
              console.log('✅ 跳转到首页成功');
            },
            fail: (err3) => {
              console.error('❌ 所有导航方案都失败了:', err3);
              uni.showToast({
                title: '导航失败，请重新打开小程序',
                icon: 'none'
              });
            }
          });
        }
      });
    }
  });
};

// 加载活动信息
const loadEventInfo = async () => {
  try {
    const response = await getEventDetailApi(eventId.value);
    if (response.code === 200 && response.data) {
      eventInfo.value = response.data;
      console.log('成功加载活动信息:', eventInfo.value);
    } else {
      throw new Error(response.msg || '获取活动信息失败');
    }
  } catch (error) {
    console.error('加载活动信息失败:', error);
    // 不显示错误提示，使用默认标题即可
  }
};

// 加载表单定义
const loadFormDefinition = async () => {
  try {
    isLoading.value = true;
    
    const response = await getFormDefinitionApi(eventId.value);
    
    if (response.code === 200 && response.data) {
      // 处理后端返回的数据：response.data 可能是JSON字符串，需要解析
      let formDefinition;
      
      if (typeof response.data === 'string') {
        try {
          // 如果是字符串，需要解析成对象
          formDefinition = JSON.parse(response.data);
          console.log('解析后的表单定义:', formDefinition);
        } catch (parseError) {
          console.error('解析表单定义JSON失败:', parseError);
          throw new Error('表单配置格式错误');
        }
      } else {
        // 如果已经是对象，直接使用
        formDefinition = response.data;
      }
      
      // 获取字段数组
      const fields = formDefinition.fields || [];
      
      if (Array.isArray(fields) && fields.length > 0) {
        formConfig.value = fields;
        console.log('成功加载表单配置，字段数量:', fields.length);
        initFormData();
        initFormRules();
      } else {
        console.warn('表单配置中没有字段或字段不是数组:', formDefinition);
        formConfig.value = [];
      }
    } else {
      throw new Error(response.msg || '获取表单配置失败');
    }
    
  } catch (error) {
    console.error('加载表单定义失败:', error);
    uni.$u.toast(error.message || '加载表单失败，请稍后重试');
    formConfig.value = [];
  } finally {
    isLoading.value = false;
  }
};

// 初始化表单数据
const initFormData = () => {
  formConfig.value.forEach(item => {
    if (item.field) {
      formData[item.field] = item.defaultValue || '';
    }
  });
};

// 初始化表单验证规则
const initFormRules = () => {
  formConfig.value.forEach(item => {
    if (item.field && item.required) {
      const rules = [{
        required: true,
        message: `请${item.type === 'select' ? '选择' : '输入'}${item.label}`,
        trigger: ['blur', 'change']
      }];
      
      // 添加特定类型的验证规则
      if (item.type === 'email') {
        rules.push({
          pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
          message: '请输入有效的邮箱地址',
          trigger: ['blur']
        });
      } else if (item.type === 'phone') {
        rules.push({
          pattern: /^1[3-9]\d{9}$/,
          message: '请输入有效的手机号',
          trigger: ['blur']
        });
      }
      
      formRules[item.field] = rules;
    }
  });
};

// 获取选择框显示值
const getSelectDisplayValue = (field, options) => {
  const value = formData[field];
  if (!value || !options) return '';
  
  const option = options.find(opt => opt.value === value);
  return option ? option.label : '';
};

// 打开选择器
const openPicker = (item) => {
  if (!item.options || !Array.isArray(item.options)) {
    uni.$u.toast('选项配置错误');
    return;
  }
  
  currentPickerField.value = item.field;
  pickerColumns.value = [item.options];
  pickerShow.value = true;
};

// 选择器确认
const onPickerConfirm = (e) => {
  const { value } = e;
  if (value && value[0] && currentPickerField.value) {
    formData[currentPickerField.value] = value[0].value;
  }
  pickerShow.value = false;
};

// 显示确认弹窗
const handleSubmit = async () => {
  if (!formRef.value) {
    uni.$u.toast('表单初始化失败');
    return;
  }

  try {
    // 表单验证
    await formRef.value.validate();

    // 验证通过后显示确认弹窗
    showConfirmModal.value = true;

  } catch (error) {
    console.error('表单验证失败:', error);
    uni.$u.toast('请检查表单填写是否正确');
  }
};

// 关闭确认弹窗
const closeConfirmModal = () => {
  showConfirmModal.value = false;
};

// 确认提交报名
const confirmSubmitRegistration = async () => {
  closeConfirmModal();

  try {
    isSubmitting.value = true;

    // 提交数据
    const response = await submitRegistrationApi({
      eventId: eventId.value,
      formData: formData
    });

    if (response.code === 200) {
      uni.$u.toast('报名成功！');

      // 🔄 状态分离重构：更新本地存储的报名状态，确保与新的分离模式兼容
      try {
        const registrationStatus = uni.getStorageSync('registrationStatus') || {};
        registrationStatus[eventId.value] = {
          isRegistered: true,
          timestamp: Date.now(),
          formData: formData, // 可选：保存表单数据供后续查看
          source: 'user_registration' // 标记数据来源
        };
        uni.setStorageSync('registrationStatus', registrationStatus);
        console.log('✅ 已在本地存储中标记报名状态（状态分离模式）:', registrationStatus);
      } catch (error) {
        console.warn('⚠️ 保存本地报名状态失败:', error);
        // 即使本地存储失败，也不影响报名流程
      }

              // 🚀 【数据实时更新方案】发送全局数据变化事件
        // 延迟跳转，让用户看到成功提示，然后发送全局广播
        setTimeout(() => {
          console.log('📤 发送数据变化广播事件...');

          // 【关键步骤】发送全局事件，通知所有监听的页面数据已发生变化
          uni.$emit('dataChanged');
          console.log('✅ 已发送 dataChanged 事件');

          // 返回到上一页（通常是活动详情页）
          uni.navigateBack({
            success: () => {
              console.log('📄 返回上一页成功');

              // 页面返回成功后，再次发送事件确保页面能收到
              setTimeout(() => {
                uni.$emit('dataChanged');
                console.log('✅ 页面返回后再次发送 dataChanged 事件');
              }, 100);
            },
            fail: (error) => {
              console.warn('📄 页面返回失败:', error);
              // 如果返回失败，跳转到首页
              uni.switchTab({
                url: '/pages/index/index'
              });
            }
          });
        }, 1500); // 让用户看到成功提示的时间

    } else {
      throw new Error(response.msg || '报名失败');
    }

  } catch (error) {
    console.error('提交报名失败:', error);

    if (error.message) {
      uni.$u.toast(error.message);
    } else {
      uni.$u.toast('提交失败，请稍后重试');
    }
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style lang="scss" scoped>
.registration-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #F2F4FA;
}

.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  z-index: 999;
}

.login-prompt-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: calc(var(--status-bar-height) + 44px); /* 状态栏高度 + 导航栏高度 */
  min-height: 60vh;
}

.empty-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: calc(var(--status-bar-height) + 44px); /* 状态栏高度 + 导航栏高度 */
  min-height: 60vh;
}

.scroll-content {
  flex: 1;
  padding-top: calc(var(--status-bar-height) + 44px); /* 状态栏高度 + 导航栏高度 */
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
  box-sizing: border-box;
}

.event-title-section {
  margin: 106rpx 30rpx 40rpx;
  text-align: center;
}

.event-title {
  width: 468rpx;
    height: 44rpx;
    font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;
    font-weight: normal;
    font-size: 32rpx;
    color: #23232A;
    font-style: normal;
    text-transform: none;
    /* 建议增加 line-height 使文字垂直居中 */
    line-height: 44rpx; 
}

.form-container {
	display: flex;
	  justify-content: center;
  background-color: transparent;
  margin: 0 30rpx 30rpx 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: none;
}

.bottom-spacer {
  height: 40rpx;
}



.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  box-sizing: border-box;
  z-index: 100;
  
  /*安全区域设置 */
  height: calc(156rpx + env(safe-area-inset-bottom));
  background-color: #FFFFFF;
  border-top: 2rpx solid #EEEEEE;
  border-radius: 0;
  box-shadow: none;
  display: flex;
  justify-content: center;
  align-items: center;
  /* 设置左右内边距，并为底部安全区域留出空间 */
  padding: 0 30rpx;
  padding-bottom: env(safe-area-inset-bottom);
}

/* uview-plus 组件样式覆盖 */
:deep(.u-form-item__label) {
  margin-bottom: 16rpx !important;
}

:deep(.u-input__content) {
  min-height: 80rpx;
}

:deep(.u-textarea) {
  background-color: transparent !important;
  border-radius: 8rpx !important;
}

/* 报名确认弹窗样式 */
.confirm-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.confirm-modal-content {
  width: 654rpx;
  height: 420rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  position: relative;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.modal-header {
  position: absolute;
  top: 42rpx;
  left: 48rpx;
  display: flex;
  align-items: center;

  .warning-icon {
    width: 48rpx;
    height: 40rpx;
    margin-right: 16rpx;
  }

  .modal-title {
    width: 142rpx;
    height: 44rpx;
    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
    font-weight: normal;
    font-size: 36rpx;
    color: #23232A;
    line-height: 44rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
}

.modal-body {
  position: absolute;
  top: 158rpx;
  left: 48rpx;

  .modal-message {
    width: 558rpx;
    height: 44rpx;
    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
    font-weight: normal;
    font-size: 32rpx;
    color: #23232A;
    line-height: 44rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}

.modal-footer {
  position: absolute;
  top: 316rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 24rpx;

  .modal-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: opacity 0.2s;

    &:active {
      opacity: 0.8;
    }
  }

  .cancel-btn {
      width: 292rpx;
      height: 76rpx;
      background: rgba(42, 97, 241, 0.1);
      border-radius: 8rpx;
      font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';
      font-weight: normal;
      font-size: 28rpx;
      color: #23232A;
      line-height: 44rpx;
  }

  .confirm-btn {
    width: 292rpx;
    height: 76rpx;
    background: #023F98;
    border-radius: 8rpx;
    font-family: 'Alibaba PuHuiTi 3.0', sans-serif;
    font-size: 28rpx;
    font-weight: 500;
    color: #ffffff;
  }
}
</style>