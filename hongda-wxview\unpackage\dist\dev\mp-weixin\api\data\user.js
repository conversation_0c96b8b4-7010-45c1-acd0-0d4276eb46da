"use strict";
const utils_request = require("../../utils/request.js");
const wxLoginApi = (code) => {
  return utils_request.post("/wxlogin", { code });
};
const getPhoneNumberApi = (code) => {
  return utils_request.post("/getPhoneNumber", { code });
};
const deleteAccountApi = () => {
  return utils_request.del("/user/delete");
};
const updateUserInfoApi = (data) => {
  return utils_request.post("/user/update", data);
};
exports.deleteAccountApi = deleteAccountApi;
exports.getPhoneNumberApi = getPhoneNumberApi;
exports.updateUserInfoApi = updateUserInfoApi;
exports.wxLoginApi = wxLoginApi;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/data/user.js.map
