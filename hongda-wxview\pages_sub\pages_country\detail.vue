<template>
  <view v-if="loading" class="loading-container">
    <uni-load-more status="loading"/>
  </view>

  <view v-else-if="country" class="page-container">
    <view class="fixed-header" :style="{ height: headerHeight + 'px' }">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="custom-nav-bar" :style="{ height: navBarHeight + 'px', paddingBottom: navBarPaddingBottomPx + 'px' }">
        <view class="nav-back-button" @click="goBack">
          <uni-icons type="left" color="#000000" size="22"></uni-icons>
        </view>
        <view class="nav-title">{{ country.nameCn || '国别详情' }}</view>
      </view>
    </view>

    <scroll-view scroll-y class="scrollable-content" :style="{ paddingTop: headerHeight + 'px' }">
      <view class="cover-section">
        <image class="cover-image" :src="baseUrl + country.detailsCoverUrl" mode="aspectFill"/>
        <view class="cover-overlay">
          <view class="country-name-cn">{{ country.nameCn }}</view>
          <view class="country-name-en">{{ country.nameEn }}</view>
        </view>
      </view>

      <view class="tabs-wrapper">
        <scroll-view class="tabs" scroll-x="true" :show-scrollbar="false">
          <view
              v-for="(tab, index) in tabs"
              :key="tab.id"
              class="tab-item"
              :class="{ active: activeTab === index }"
              @click="onTabClick(index)"
          >
            <image class="tab-icon" :src="activeTab === index ? tab.activeIcon : tab.icon"></image>
            <text class="tab-text">{{ tab.name }}</text>
          </view>
        </scroll-view>
      </view>

      <view class="content-wrapper">
        <view v-if="!isPolicyTabActive">
          <view v-show="activeTab === 0" class="content-panel">
            <view class="section-card">
              <view class="section-title">国家简介</view>
              <rich-text class="rich-text-content" :nodes="country.introduction"></rich-text>
            </view>

            <view class="section-card" v-if="basicInfoList.length > 0">
              <view class="section-title">基本信息</view>
              <view class="info-grid-container basic-info-card">
                <view class="info-pair-row" v-for="(pair, pairIndex) in pairedBasicInfoList" :key="pairIndex">
                  <view class="info-row key-row">
                    <view class="info-column">{{ pair[0]?.key }}</view>
                    <view class="info-column">{{ pair[1]?.key }}</view>
                  </view>
                  <view class="info-row value-row">
                    <view class="info-column">{{ pair[0]?.value }}</view>
                    <view class="info-column">{{ pair[1]?.value }}</view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <view v-show="activeTab === 4" class="content-panel">
            <view
                class="park-card"
                v-for="park in country.industrialParks"
                :key="park.id"
                @click="goToParkDetail(park.id)"
            >
              <view class="park-card-image-wrapper">
                <u-image
                    :src="baseUrl + park.coverImageUrl"
                    width="100%"
                    height="240rpx"
                    :fade="true"
                    :lazy-load="true"
                ></u-image>
              </view>
              <view class="park-card-content">
                <view class="park-name">{{ park.name }}</view>
                <view class="park-info-item">
                  <image class="park-info-icon" src="/static/icons/位置icon金@2x.png" mode="aspectFit"></image>
                  <text>{{ park.location }}</text>
                </view>
                <view class="park-info-item">
                  <image class="park-info-icon" src="/static/icons/企业浅金@2x.png" mode="aspectFit"></image>
                  <text>{{ park.industries }}</text>
                </view>
                <view class="park-info-item">
                  <image class="park-info-icon" src="/static/icons/亮点浅金@2x.png" mode="aspectFit"></image>
                  <text>{{ park.features }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view v-else class="policy-page-container">
          <view class="policy-title-header">
            <h1 class="policy-title">{{ currentPolicyName }}</h1>
          </view>
          <view class="policy-main-content">
            <view class="policy-intro-wrapper">
              <rich-text class="rich-text-content" :nodes="currentPolicyContent"></rich-text>
            </view>
            <ContentModule
                :key="currentPolicyType"
                :country-id="country.id"
                :policy-type="currentPolicyType"
            />
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>


<script setup>
import {onLoad} from '@dcloudio/uni-app'
import {computed, ref} from 'vue';
import {getCountryDetail} from '@/api/content/country.js';
import {IMAGE_BASE_URL} from '@/utils/config.js';
import ContentModule from '@/components/home/<USER>';

// --- [新增] 自定义导航栏相关逻辑 ---
// 1. 定义想要的额外间距（单位rpx）
const navBarPaddingBottomRpx = 20;
// 2. 将 rpx 转换为 px，用于 JS 计算
const navBarPaddingBottomPx = uni.upx2px(navBarPaddingBottomRpx);

const statusBarHeight = ref(0);
const navBarHeight = ref(0);
const headerHeight = ref(0);

const getNavBarInfo = () => {
  try {
    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
    statusBarHeight.value = menuButtonInfo.top;
    navBarHeight.value = menuButtonInfo.height;
    // 3. 在总高度计算中使用转换后的 px 值
    headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;
  } catch(e) {
    const systemInfo = uni.getSystemInfoSync();
    statusBarHeight.value = systemInfo.statusBarHeight || 20;
    navBarHeight.value = 44;
    // 4. 在回退方案中也使用转换后的 px 值
    headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;
  }
};

const goBack = () => {
  uni.navigateBack({ delta: 1 });
};
// --- 导航栏逻辑结束 ---


const baseUrl = IMAGE_BASE_URL;

const country = ref(null);
const loading = ref(true);
const activeTab = ref(0);

const tabs = ref([
  { id: 'basic', name: '基本信息', icon: '/static/icons/基本信息icon浅色@2x.png', activeIcon: '/static/icons/基本信息icon深色@2x.png' },
  { id: 'investment', name: '招商政策', icon: '/static/icons/招商政策icon浅色@2x.png', activeIcon: '/static/icons/招商政策icon深色@2x.png' },
  { id: 'customs', name: '海关政策', icon: '/static/icons/海关icon浅色@2x.png', activeIcon: '/static/icons/海关icon深色@2x.png' },
  { id: 'tax', name: '税务政策', icon: '/static/icons/税务icon浅色@2x.png', activeIcon: '/static/icons/税务icon深色@2x.png' },
  { id: 'parks', name: '工业园区', icon: '/static/icons/工业园区icon浅色@2x.png', activeIcon: '/static/icons/工业园区icon深色@2x.png' },
]);

const basicInfoList = computed(() => {
  if (country.value && Array.isArray(country.value.basicInfoJson)) {
    return country.value.basicInfoJson;
  }
  return [];
});

const pairedBasicInfoList = computed(() => {
  const result = [];
  const list = basicInfoList.value;
  for (let i = 0; i < list.length; i += 2) {
    const pair = [list[i]];
    if (list[i + 1]) {
      pair.push(list[i + 1]);
    }
    result.push(pair);
  }
  return result;
});

const onTabClick = (index) => {
  activeTab.value = index;
};

const isPolicyTabActive = computed(() => {
  const policyIds = ['investment', 'customs', 'tax'];
  return policyIds.includes(tabs.value[activeTab.value]?.id);
});

const currentPolicyType = computed(() => {
  return tabs.value[activeTab.value]?.id;
});

const currentPolicyName = computed(() => {
  return tabs.value[activeTab.value]?.name;
});

const currentPolicyContent = computed(() => {
  if (!country.value) return '';
  switch (currentPolicyType.value) {
    case 'investment': return country.value.investmentPolicy;
    case 'customs': return country.value.customsPolicy;
    case 'tax': return country.value.taxPolicy;
    default: return '';
  }
});

const goToParkDetail = (parkId) => {
  uni.navigateTo({
    url: `/pages_sub/pages_other/park_detail?id=${parkId}`
  });
};

onLoad(async (options) => {
  // 获取导航栏尺寸
  getNavBarInfo();

  // 验证国别ID是否存在
  if (!options.id) {
    uni.showToast({title: '参数错误', icon: 'none'});
    uni.navigateBack();
    return;
  }

  // --- [核心修改] 处理从首页传来的tab参数 ---
  if (options.tab) {
    // 1. 在tabs数组中查找与参数匹配的项的索引
    const initialTabIndex = tabs.value.findIndex(t => t.id === options.tab);

    // 2. 如果找到了匹配的tab (findIndex不返回-1)
    if (initialTabIndex !== -1) {
      // 3. 就将activeTab的初始值设置为该索引
      activeTab.value = initialTabIndex;
    }
  }
  // --- 修改结束 ---

  // 后续的数据请求逻辑保持不变
  try {
    const res = await getCountryDetail(options.id);
    country.value = res.data;
  } catch (error) {
    console.error('Fetch detail error:', error);
    uni.showToast({title: error.message || '加载失败', icon: 'none'});
  } finally {
    loading.value = false;
  }
});
</script>

<style lang="scss" scoped>
/* --- [新增] 页面布局及自定义导航栏样式 --- */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.scrollable-content {
  flex: 1;
  height: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  background-color: #FFFFFF; /* [修改] 滚动区背景也设为白色，避免露馅 */
}

.status-bar {
  width: 100%;
}

.custom-nav-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-sizing: content-box; /* [新增] 确保 padding 不会影响内部元素对齐 */
}

.nav-back-button {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #000000;
}
/* --- 导航栏样式结束 --- */


.loading-container {
  padding-top: 200rpx;
}

.cover-section {
  position: relative;
  height: 400rpx;

  .cover-image {
    width: 100%;
    height: 100%;
  }

  .cover-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 30rpx;
    box-sizing: border-box;
    color: #fff;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent);

    .country-name-cn {
      font-size: 48rpx;
      font-weight: bold;
    }

    .country-name-en {
      font-size: 32rpx;
      opacity: 0.9;
    }
  }
}

.tabs-wrapper {
  margin: 30rpx 30rpx 0;
  background-color: #F4F4F4;
  border-radius: 16rpx;
  padding: 12rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.tabs {
  display: flex;
  white-space: nowrap;
}

.tab-item {
  display: inline-flex;
  align-items: center;
  gap: 12rpx;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;

  &:last-child {
    margin-right: 0;
  }

  .tab-icon {
    width: 40rpx;
    height: 40rpx;
  }

  .tab-text {
    font-size: 28rpx;
    color: #9B9A9A;
  }

  &.active {
    background-image: url('http://47.122.155.110:9000/hongda-public/system%2FFrame%201_slices%2F%E9%87%91%E8%89%B2%E8%A7%92%E6%A0%87%402x.png');
    background-size: cover;
    background-position: center;

    .tab-text {
      color: #23232A;
      font-weight: bold;
    }
  }
}

.content-wrapper {
  padding: 24rpx;
}

.section-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  padding-bottom: 15rpx;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.rich-text-content {
  font-size: 26rpx;
  color: #23232A;
}

.basic-info-card {
  background-image: url('http://47.122.155.110:9000/hongda-public/system%2FFrame%201_slices%2F%E5%9F%BA%E6%9C%AC%E4%BF%A1%E6%81%AF%E5%BA%95%E5%9B%BE%402x.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 20rpx;
}

.info-grid-container {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.info-row {
  display: flex;
  width: 100%;
}

.info-column {
  flex: 1;
  width: 50%;
  padding-right: 20rpx;
  box-sizing: border-box;
}

.key-row .info-column {
  font-size: 24rpx;
  color: #66666E;
}

.value-row {
  margin-top: 8rpx;

  .info-column {
    font-size: 28rpx;
    color: #23232A;
    font-weight: 500;
  }
}

.policy-page-container {
  // 容器本身不需要额外样式
}

.policy-title-header {
  padding: 30rpx;
  border-radius: 16rpx 16rpx 0 0;
  background: linear-gradient(to bottom, #CEDEF5, #F0F2F3) center;

  .policy-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #023F98;
  }
}

.policy-main-content {
  background-color: #F2F4FA;
  border-radius: 0 0 16rpx 16rpx;
  padding: 30rpx;
}

.policy-intro-wrapper {
  margin-bottom: 30rpx;
}

.park-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
  overflow: hidden;

  &:active {
    transform: scale(0.98);
  }

  .park-card-image-wrapper {
    width: 100%;
    height: 240rpx;
    background-color: #f0f2f5;
  }

  .park-card-content {
    padding: 30rpx;
  }

  .park-name {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
  }

  .park-info-item {
    display: flex;
    align-items: center;
    font-size: 26rpx;
    color: #666;
    margin-bottom: 16rpx;

    uni-icons {
      margin-right: 12rpx;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .park-info-icon {
    width: 26rpx;
    height: 26rpx;
    margin-right: 12rpx;
    flex-shrink: 0;
  }
}
</style>