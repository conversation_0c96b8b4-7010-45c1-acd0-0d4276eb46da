"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_platform_ad = require("../../api/platform/ad.js");
if (!Math) {
  (common_vendor.unref(HeaderComponent) + common_vendor.unref(BannerComponent) + common_vendor.unref(QuickNavigationComponent) + common_vendor.unref(CountryHighlightComponent) + EventPromotionComponent + common_vendor.unref(NewsListComponent) + CustomTabBar)();
}
const HeaderComponent = () => "../../components/home/<USER>";
const BannerComponent = () => "../../components/home/<USER>";
const QuickNavigationComponent = () => "../../components/home/<USER>";
const CountryHighlightComponent = () => "../../components/home/<USER>";
const NewsListComponent = () => "../../components/home/<USER>";
const EventPromotionComponent = () => "../../components/home/<USER>";
const CustomTabBar = () => "../../components/layout/CustomTabBar.js";
const AD_POSITION_CODE = "splash_screen";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const showPopupAd = common_vendor.ref(false);
    const popupAdData = common_vendor.ref({});
    const checkAndShowPopupAd = async () => {
      try {
        const response = await api_platform_ad.getAdListByPositionApi(AD_POSITION_CODE, {
          pageSize: 1
        });
        if (response && response.data && response.data.length > 0) {
          popupAdData.value = response.data[0];
          showPopupAd.value = true;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:66", "获取弹窗广告失败:", error.message || error);
      }
    };
    const navigateToService = () => {
      common_vendor.index.navigateTo({
        url: "/pages_sub/pages_profile/contact"
      });
    };
    common_vendor.onShow(() => {
      common_vendor.index.hideTabBar();
      checkAndShowPopupAd();
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          current: 0
        }),
        b: common_assets._imports_0,
        c: common_vendor.o(navigateToService)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1cf27b2a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
