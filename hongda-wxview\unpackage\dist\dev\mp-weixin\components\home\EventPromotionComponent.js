"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_platform_ad = require("../../api/platform/ad.js");
const utils_image = require("../../utils/image.js");
if (!Array) {
  const _easycom_u_skeleton2 = common_vendor.resolveComponent("u-skeleton");
  const _easycom_up_button2 = common_vendor.resolveComponent("up-button");
  (_easycom_u_skeleton2 + _easycom_up_button2)();
}
const _easycom_u_skeleton = () => "../../uni_modules/uview-plus/components/u-skeleton/u-skeleton.js";
const _easycom_up_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
if (!Math) {
  (_easycom_u_skeleton + _easycom_up_button)();
}
const POSITION_CODE = "HOME_PROMO_EVENT";
const _sfc_main = {
  __name: "EventPromotionComponent",
  setup(__props) {
    const promoDataList = common_vendor.ref([]);
    const isLoading = common_vendor.ref(true);
    const currentIndex = common_vendor.ref(0);
    const fetchPromoData = async () => {
      try {
        const response = await api_platform_ad.getAdListByPositionApi(POSITION_CODE, {
          pageSize: 10
        });
        common_vendor.index.__f__("log", "at components/home/<USER>", "活动推广API响应:", response);
        if (response.data && Array.isArray(response.data)) {
          response.data.forEach((ad, index) => {
            common_vendor.index.__f__("log", "at components/home/<USER>", `广告${index + 1}的iconUrl:`, ad.iconUrl);
          });
        }
        if (response.data && Array.isArray(response.data) && response.data.length > 0) {
          promoDataList.value = response.data.map((ad) => {
            const processedData = {
              id: ad.id,
              title: ad.title,
              image: utils_image.getFullImageUrl(ad.imageUrl),
              iconUrl: utils_image.getFullImageUrl(ad.iconUrl),
              // 修复：使用getFullImageUrl处理图标URL
              linkUrl: ad.finalLinkUrl || ad.linkUrl,
              // 优先使用最终跳转链接
              // 使用后端返回的活动简介和卖点字段，如果为空则提供默认文案
              descriptionLine1: ad.eventSummary || "官方认证，品质保证",
              descriptionLine2: ad.eventSellPoint || "干货满满，不容错过"
            };
            common_vendor.index.__f__("log", "at components/home/<USER>", "活动推广图标调试:", {
              原始iconUrl: ad.iconUrl,
              处理后iconUrl: processedData.iconUrl,
              title: ad.title
            });
            return processedData;
          });
        } else {
          common_vendor.index.__f__("warn", "at components/home/<USER>", "未找到推广数据或返回格式异常:", response);
          promoDataList.value = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at components/home/<USER>", "获取推广数据失败:", error);
      } finally {
        isLoading.value = false;
      }
    };
    const onSwiperChange = (e) => {
      currentIndex.value = e.detail.current;
    };
    const switchToSlide = (index) => {
      currentIndex.value = index;
      common_vendor.index.__f__("log", "at components/home/<USER>", "点击指示器切换到索引:", index);
    };
    const handlePromoClick = (promoItem) => {
      if (promoItem && promoItem.linkUrl) {
        const linkUrl = promoItem.linkUrl;
        common_vendor.index.__f__("log", "at components/home/<USER>", "准备跳转到:", linkUrl);
        if (linkUrl.startsWith("http")) {
          common_vendor.index.navigateTo({
            url: `/pages/webview/index?url=${encodeURIComponent(linkUrl)}&title=${encodeURIComponent(promoItem.title || "详情")}`
          });
        } else {
          common_vendor.index.navigateTo({
            url: linkUrl
          });
        }
      } else {
        common_vendor.index.__f__("warn", "at components/home/<USER>", "推广卡片跳转链接为空");
      }
    };
    const onImageLoad = (e) => {
      common_vendor.index.__f__("log", "at components/home/<USER>", "图片加载成功");
    };
    const onImageError = (e) => {
      common_vendor.index.__f__("error", "at components/home/<USER>", "图片加载失败:", e);
    };
    const onIconLoad = (e) => {
      common_vendor.index.__f__("log", "at components/home/<USER>", "图标加载成功");
    };
    const onIconError = (e) => {
      common_vendor.index.__f__("error", "at components/home/<USER>", "图标加载失败:", e);
    };
    common_vendor.onMounted(() => {
      fetchPromoData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: isLoading.value
      }, isLoading.value ? {
        b: common_vendor.p({
          loading: true,
          animate: true,
          rows: 4,
          title: true,
          titleWidth: "60%",
          rowsWidth: "['100%', '40%', '40%', '100%']",
          rowsHeight: "['180px', '20px', '20px', '40px']"
        })
      } : promoDataList.value.length > 0 ? common_vendor.e({
        d: common_vendor.f(promoDataList.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.iconUrl || "/static/event/美妆logo蓝@2x.png",
            b: common_vendor.o(onIconError, item.id || index),
            c: common_vendor.o(onIconLoad, item.id || index),
            d: common_vendor.t(item.title),
            e: item.image,
            f: common_vendor.o(onImageError, item.id || index),
            g: common_vendor.o(onImageLoad, item.id || index),
            h: item.descriptionLine1
          }, item.descriptionLine1 ? {
            i: common_assets._imports_0$8,
            j: common_vendor.t(item.descriptionLine1)
          } : {}, {
            k: item.descriptionLine2
          }, item.descriptionLine2 ? {
            l: common_assets._imports_1$6,
            m: common_vendor.t(item.descriptionLine2)
          } : {}, {
            n: "4eff421e-1-" + i0,
            o: common_vendor.o(($event) => handlePromoClick(item), item.id || index),
            p: item.id || index
          });
        }),
        e: common_vendor.p({
          type: "primary",
          shape: "square",
          text: "立即报名",
          size: "large",
          customStyle: {
            backgroundColor: "#0052D9",
            /*蓝色 */
            height: "70rpx"
          }
        }),
        f: currentIndex.value,
        g: common_vendor.o(onSwiperChange),
        h: promoDataList.value.length > 1
      }, promoDataList.value.length > 1 ? {
        i: common_vendor.f(promoDataList.value, (item, index, i0) => {
          return {
            a: index,
            b: currentIndex.value === index ? 1 : "",
            c: common_vendor.o(($event) => switchToSlide(index), index)
          };
        })
      } : {}) : {}, {
        c: promoDataList.value.length > 0
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-4eff421e"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/home/<USER>
