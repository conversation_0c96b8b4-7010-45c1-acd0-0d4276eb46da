{"version": 3, "file": "registration.js", "sources": ["pages_sub/pages_event/registration.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfc3ViXHBhZ2VzX2V2ZW50XHJlZ2lzdHJhdGlvbi52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"registration-page\">\r\n    <!-- 自定义导航栏 -->\r\n    <up-navbar\r\n      :title=\"eventInfo?.title || '活动报名'\"\r\n      :fixed=\"true\"\r\n      :safeAreaInsetTop=\"true\"\r\n      bgColor=\"transparent\"\r\n      leftIcon=\"arrow-left\"\r\n      leftIconColor=\"#333333\"\r\n      titleStyle=\"color: #333333; font-weight: bold;\"\r\n      @leftClick=\"handleNavBack\"\r\n    />\r\n\r\n    <!-- 全局加载状态 -->\r\n    <view class=\"loading-container\" v-if=\"isLoading\">\r\n      <up-loading-page loadingText=\"正在加载报名表单...\" loadingMode=\"spinner\"></up-loading-page>\r\n    </view>\r\n\r\n    <!-- 未登录状态提示 -->\r\n    <view class=\"login-prompt-container\" v-else-if=\"!isLoggedIn\">\r\n      <up-empty \r\n        mode=\"permission\" \r\n        text=\"请先登录后进行报名\"\r\n        textColor=\"#909399\"\r\n        textSize=\"28\"\r\n      >\r\n        <template #button>\r\n          <up-button \r\n            type=\"primary\" \r\n            text=\"立即登录\"\r\n            @click=\"goToLogin\"\r\n            customStyle=\"background-color: #f56c6c; border-color: #f56c6c; width: 200rpx; height: 70rpx; font-size: 28rpx;\"\r\n          ></up-button>\r\n        </template>\r\n      </up-empty>\r\n    </view>\r\n\r\n    <!-- 空状态：无表单配置或加载失败 -->\r\n    <view class=\"empty-container\" v-else-if=\"isLoggedIn && (!formConfig || formConfig.length === 0)\">\r\n      <up-empty \r\n        mode=\"data\" \r\n        text=\"该活动无需报名表单\"\r\n        textColor=\"#909399\"\r\n        textSize=\"28\"\r\n      ></up-empty>\r\n    </view>\r\n\r\n    <!-- 主要内容区域：动态表单 (只有登录用户才能看到) -->\r\n    <scroll-view scroll-y class=\"scroll-content\" v-else-if=\"isLoggedIn && formConfig && formConfig.length > 0\">\r\n      \r\n      <!-- 活动名称显示区域 -->\r\n      <view class=\"event-title-section\" v-if=\"eventInfo\">\r\n        <text class=\"event-title\">{{ eventInfo.title }}</text>\r\n      </view>\r\n\r\n      <!-- 动态表单 -->\r\n      <view class=\"form-container\">\r\n        <up-form\r\n          :model=\"formData\"\r\n          :rules=\"formRules\"\r\n          ref=\"formRef\"\r\n          labelPosition=\"top\"\r\n          labelWidth=\"auto\"\r\n          :labelStyle=\"{\r\n             fontFamily: 'Alibaba PuHuiTi 3.0-55 Regular',\r\n                 fontWeight: 'normal',\r\n                 fontSize: '28rpx',\r\n                 color: '#23232A',\r\n                 lineHeight: 'normal'\r\n            }\"\r\n        >\r\n          <!-- 动态渲染表单项 -->\r\n          <template v-for=\"(item, index) in formConfig\" :key=\"index\">\r\n            \r\n            <!-- 单行输入框 -->\r\n            <up-form-item \r\n              v-if=\"item.type === 'input'\"\r\n              :label=\"item.label\"\r\n              :prop=\"item.field\"\r\n              :required=\"item.required\"\r\n              class=\"form-item\"\r\n              :style=\"{ marginBottom: '92rpx' }\"\r\n            >\r\n              <up-input\r\n                v-model=\"formData[item.field]\"\r\n                :placeholder=\"item.props?.placeholder || '请输入'\"\r\n                :clearable=\"true\"\r\n                :maxlength=\"item.props?.maxlength || 100\"\r\n                customStyle=\"width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;\"\r\n              ></up-input>\r\n            </up-form-item>\r\n\r\n            <!-- 多行文本输入 -->\r\n            <up-form-item \r\n              v-else-if=\"item.type === 'textarea'\"\r\n              :label=\"item.label\"\r\n              :prop=\"item.field\"\r\n              :required=\"item.required\"\r\n              class=\"form-item\"\r\n              :style=\"{ marginBottom: '92rpx' }\"\r\n            >\r\n              <up-textarea\r\n                v-model=\"formData[item.field]\"\r\n                :placeholder=\"item.props?.placeholder || '请输入'\"\r\n                :maxlength=\"item.props?.maxlength || 500\"\r\n                height=\"120\"\r\n                count\r\n                customStyle=\"width: 686rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;\"\r\n              ></up-textarea>\r\n            </up-form-item>\r\n\r\n            <!-- 下拉选择框 -->\r\n            <up-form-item \r\n              v-else-if=\"item.type === 'select'\"\r\n              :label=\"item.label\"\r\n              :prop=\"item.field\"\r\n              :required=\"item.required\"\r\n              class=\"form-item\"\r\n              :style=\"{ marginBottom: '92rpx' }\"\r\n            >\r\n              <up-input\r\n                :value=\"getSelectDisplayValue(item.field, item.options)\"\r\n                :placeholder=\"item.props?.placeholder || '请选择'\"\r\n                readonly\r\n                suffixIcon=\"arrow-down-fill\"\r\n                @click=\"openPicker(item)\"\r\n                customStyle=\"width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;\"\r\n              ></up-input>\r\n            </up-form-item>\r\n\r\n            <!-- 数字输入框 -->\r\n            <up-form-item \r\n              v-else-if=\"item.type === 'number'\"\r\n              :label=\"item.label\"\r\n              :prop=\"item.field\"\r\n              :required=\"item.required\"\r\n              class=\"form-item\"\r\n              :style=\"{ marginBottom: '92rpx' }\"\r\n            >\r\n              <up-input\r\n                v-model=\"formData[item.field]\"\r\n                :placeholder=\"item.props?.placeholder || '请输入数字'\"\r\n                type=\"number\"\r\n                :clearable=\"true\"\r\n                customStyle=\"width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;\"\r\n              ></up-input>\r\n            </up-form-item>\r\n\r\n            <!-- 手机号输入框 -->\r\n            <up-form-item \r\n              v-else-if=\"item.type === 'phone'\"\r\n              :label=\"item.label\"\r\n              :prop=\"item.field\"\r\n              :required=\"item.required\"\r\n              class=\"form-item\"\r\n              :style=\"{ marginBottom: '92rpx' }\"\r\n            >\r\n              <up-input\r\n                v-model=\"formData[item.field]\"\r\n                :placeholder=\"item.props?.placeholder || '请输入手机号'\"\r\n                type=\"number\"\r\n                :clearable=\"true\"\r\n                :maxlength=\"11\"\r\n                customStyle=\"width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;\"\r\n              ></up-input>\r\n            </up-form-item>\r\n\r\n            <!-- 邮箱输入框 -->\r\n            <up-form-item \r\n              v-else-if=\"item.type === 'email'\"\r\n              :label=\"item.label\"\r\n              :prop=\"item.field\"\r\n              :required=\"item.required\"\r\n              class=\"form-item\"\r\n              :style=\"{ marginBottom: '92rpx' }\"\r\n            >\r\n              <up-input\r\n                v-model=\"formData[item.field]\"\r\n                :placeholder=\"item.props?.placeholder || '请输入邮箱地址'\"\r\n                :clearable=\"true\"\r\n                customStyle=\"width: 686rpx; height: 76rpx; background: #FFFFFF; border-radius: 8rpx; border: 2rpx solid #CBCBCB; padding: 20rpx; box-sizing: border-box;\"\r\n              ></up-input>\r\n            </up-form-item>\r\n\r\n          </template>\r\n        </up-form>\r\n      </view>\r\n\r\n      <!-- 底部留白，为固定按钮腾出空间 -->\r\n      <view class=\"bottom-spacer\"></view>\r\n    </scroll-view>\r\n\r\n    <!-- 底部提交按钮 (只有登录用户且有表单时才显示) -->\r\n    <view class=\"bottom-action-bar\" v-if=\"isLoggedIn && !isLoading && formConfig && formConfig.length > 0\">\r\n      <up-button \r\n        type=\"primary\"\r\n        shape=\"circle\"\r\n        size=\"large\"\r\n        :loading=\"isSubmitting\"\r\n        :disabled=\"isSubmitting\"\r\n        @click=\"handleSubmit\"\r\n        customStyle=\"width: 702rpx; height: 76rpx; background: #023F98; border-radius: 8rpx; border-color: #023F98; font-size: 28rpx;\"\r\n      >\r\n        {{ isSubmitting ? '提交中...' : '提交报名信息' }}\r\n      </up-button>\r\n    </view>\r\n\r\n    <!-- 选择器弹窗 -->\r\n    <up-picker\r\n      ref=\"pickerRef\"\r\n      :show=\"pickerShow\"\r\n      :columns=\"pickerColumns\"\r\n      @confirm=\"onPickerConfirm\"\r\n      @cancel=\"pickerShow = false\"\r\n      keyName=\"label\"\r\n    ></up-picker>\r\n\r\n    <!-- 报名确认弹窗 -->\r\n    <view v-if=\"showConfirmModal\" class=\"confirm-modal-overlay\" @click=\"closeConfirmModal\">\r\n      <view class=\"confirm-modal-content\" @click.stop>\r\n        <!-- 警告图标和标题 -->\r\n        <view class=\"modal-header\">\r\n          <image class=\"warning-icon\" src=\"/static/profile/orders_warning.png\" mode=\"aspectFit\"></image>\r\n          <text class=\"modal-title\">操作提示</text>\r\n        </view>\r\n\r\n        <!-- 提示内容 -->\r\n        <view class=\"modal-body\">\r\n          <text class=\"modal-message\">确认提交报名信息？</text>\r\n        </view>\r\n\r\n        <!-- 按钮组 -->\r\n        <view class=\"modal-footer\">\r\n          <view class=\"modal-btn cancel-btn\" @click=\"closeConfirmModal\">\r\n            暂不提交\r\n          </view>\r\n          <view class=\"modal-btn confirm-btn\" @click=\"confirmSubmitRegistration\">\r\n            确认提交\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport {reactive, ref} from 'vue';\r\nimport {onLoad, onShow} from '@dcloudio/uni-app';\r\nimport {getFormDefinitionApi, submitRegistrationApi} from '@/pages_sub/api/data/registration.js';\r\nimport {getEventDetailApi} from '@/api/data/event.js';\r\n\r\n// 响应式数据\r\nconst eventId = ref(null);\r\nconst eventInfo = ref(null); // 新增：活动信息\r\nconst formConfig = ref([]);\r\nconst formData = reactive({});\r\nconst formRules = reactive({});\r\nconst isLoading = ref(true);\r\nconst isSubmitting = ref(false);\r\nconst isLoggedIn = ref(false); // 新增：登录状态管理\r\n\r\n// 选择器相关\r\nconst pickerShow = ref(false);\r\nconst pickerColumns = ref([]);\r\nconst currentPickerField = ref('');\r\n\r\n// 确认弹窗相关\r\nconst showConfirmModal = ref(false);\r\n\r\n// 表单引用\r\nconst formRef = ref();\r\nconst pickerRef = ref();\r\n\r\n// 检查登录状态的函数\r\nconst checkLoginStatus = () => {\r\n  const token = uni.getStorageSync('token');\r\n  return !!token; // 转换为布尔值\r\n};\r\n\r\n// 页面加载 - 只处理基础参数，不进行登录检查\r\nonLoad(async (options) => {\r\n  eventId.value = options.id;\r\n  \r\n  if (!eventId.value) {\r\n    uni.$u.toast('活动信息错误');\r\n    setTimeout(() => {\r\n      uni.navigateBack();\r\n    }, 1500);\r\n    return;\r\n  }\r\n  \r\n  // 注意：这里不再调用 loadFormDefinition()，移动到 onShow 中\r\n});\r\n\r\n// 页面显示时的逻辑 - 核心登录检查逻辑\r\nonShow(async () => {\r\n  console.log('=== 报名页面 onShow 触发 ===');\r\n  \r\n  // 每次页面显示时都重新检查登录状态\r\n  const currentLoginStatus = checkLoginStatus();\r\n  isLoggedIn.value = currentLoginStatus;\r\n  \r\n  console.log('当前登录状态:', currentLoginStatus);\r\n  console.log('当前token:', uni.getStorageSync('token'));\r\n  \r\n  if (!currentLoginStatus) {\r\n    // 用户未登录，立即返回上一页（detail页面）\r\n    console.log('用户未登录，返回上一页让detail页面处理登录跳转');\r\n    isLoading.value = false; // 停止加载状态\r\n    \r\n    uni.showToast({\r\n      title: '请先登录后报名',\r\n      icon: 'none',\r\n      duration: 1500\r\n    });\r\n    \r\n    // 立即返回上一页，避免在registration页面进行登录跳转\r\n    setTimeout(() => {\r\n      console.log('返回上一页（detail页面）');\r\n      uni.navigateBack({\r\n        fail: () => {\r\n          // 如果返回失败，说明可能是直接访问的registration页面\r\n          console.log('返回失败，跳转到首页');\r\n          uni.switchTab({ url: '/pages/index/index' });\r\n        }\r\n      });\r\n    }, 800);\r\n    \r\n  } else {\r\n    // 用户已登录，可以正常进行报名操作\r\n    console.log('用户已登录，开始加载活动信息和表单数据');\r\n    \r\n    // 加载活动信息（无论是否已加载过都重新加载，确保信息是最新的）\r\n    if (eventId.value) {\r\n      await loadEventInfo();\r\n    }\r\n    \r\n    // 只有在已登录的情况下才加载表单\r\n    if (eventId.value && (!formConfig.value || formConfig.value.length === 0)) {\r\n      await loadFormDefinition();\r\n    } else {\r\n      // 如果表单已经加载过，直接停止loading状态\r\n      isLoading.value = false;\r\n    }\r\n  }\r\n});\r\n\r\n// 跳转到登录页的方法\r\nconst goToLogin = () => {\r\n  console.log('手动跳转到登录页');\r\n  uni.navigateTo({\r\n    url: '/pages/login/index'\r\n  });\r\n};\r\n\r\n// 智能导航回退处理函数\r\nconst handleNavBack = () => {\r\n  console.log('=== 开始智能导航回退处理 ===');\r\n  \r\n  // 第一步：尝试正常回退\r\n  uni.navigateBack({\r\n    success: () => {\r\n      console.log('✅ 正常回退成功');\r\n    },\r\n    fail: (err) => {\r\n      console.warn('⚠️ 正常回退失败:', err);\r\n      \r\n      // 第二步：尝试跳转到活动列表页面\r\n      uni.navigateTo({\r\n        url: '/pages/event/index',\r\n        success: () => {\r\n          console.log('✅ 跳转到活动列表页面成功');\r\n        },\r\n        fail: (err2) => {\r\n          console.warn('⚠️ 跳转到活动列表页面失败:', err2);\r\n          \r\n          // 第三步：最后的兜底方案，跳转到首页\r\n          uni.switchTab({\r\n            url: '/pages/index/index',\r\n            success: () => {\r\n              console.log('✅ 跳转到首页成功');\r\n            },\r\n            fail: (err3) => {\r\n              console.error('❌ 所有导航方案都失败了:', err3);\r\n              uni.showToast({\r\n                title: '导航失败，请重新打开小程序',\r\n                icon: 'none'\r\n              });\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n  });\r\n};\r\n\r\n// 加载活动信息\r\nconst loadEventInfo = async () => {\r\n  try {\r\n    const response = await getEventDetailApi(eventId.value);\r\n    if (response.code === 200 && response.data) {\r\n      eventInfo.value = response.data;\r\n      console.log('成功加载活动信息:', eventInfo.value);\r\n    } else {\r\n      throw new Error(response.msg || '获取活动信息失败');\r\n    }\r\n  } catch (error) {\r\n    console.error('加载活动信息失败:', error);\r\n    // 不显示错误提示，使用默认标题即可\r\n  }\r\n};\r\n\r\n// 加载表单定义\r\nconst loadFormDefinition = async () => {\r\n  try {\r\n    isLoading.value = true;\r\n    \r\n    const response = await getFormDefinitionApi(eventId.value);\r\n    \r\n    if (response.code === 200 && response.data) {\r\n      // 处理后端返回的数据：response.data 可能是JSON字符串，需要解析\r\n      let formDefinition;\r\n      \r\n      if (typeof response.data === 'string') {\r\n        try {\r\n          // 如果是字符串，需要解析成对象\r\n          formDefinition = JSON.parse(response.data);\r\n          console.log('解析后的表单定义:', formDefinition);\r\n        } catch (parseError) {\r\n          console.error('解析表单定义JSON失败:', parseError);\r\n          throw new Error('表单配置格式错误');\r\n        }\r\n      } else {\r\n        // 如果已经是对象，直接使用\r\n        formDefinition = response.data;\r\n      }\r\n      \r\n      // 获取字段数组\r\n      const fields = formDefinition.fields || [];\r\n      \r\n      if (Array.isArray(fields) && fields.length > 0) {\r\n        formConfig.value = fields;\r\n        console.log('成功加载表单配置，字段数量:', fields.length);\r\n        initFormData();\r\n        initFormRules();\r\n      } else {\r\n        console.warn('表单配置中没有字段或字段不是数组:', formDefinition);\r\n        formConfig.value = [];\r\n      }\r\n    } else {\r\n      throw new Error(response.msg || '获取表单配置失败');\r\n    }\r\n    \r\n  } catch (error) {\r\n    console.error('加载表单定义失败:', error);\r\n    uni.$u.toast(error.message || '加载表单失败，请稍后重试');\r\n    formConfig.value = [];\r\n  } finally {\r\n    isLoading.value = false;\r\n  }\r\n};\r\n\r\n// 初始化表单数据\r\nconst initFormData = () => {\r\n  formConfig.value.forEach(item => {\r\n    if (item.field) {\r\n      formData[item.field] = item.defaultValue || '';\r\n    }\r\n  });\r\n};\r\n\r\n// 初始化表单验证规则\r\nconst initFormRules = () => {\r\n  formConfig.value.forEach(item => {\r\n    if (item.field && item.required) {\r\n      const rules = [{\r\n        required: true,\r\n        message: `请${item.type === 'select' ? '选择' : '输入'}${item.label}`,\r\n        trigger: ['blur', 'change']\r\n      }];\r\n      \r\n      // 添加特定类型的验证规则\r\n      if (item.type === 'email') {\r\n        rules.push({\r\n          pattern: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\r\n          message: '请输入有效的邮箱地址',\r\n          trigger: ['blur']\r\n        });\r\n      } else if (item.type === 'phone') {\r\n        rules.push({\r\n          pattern: /^1[3-9]\\d{9}$/,\r\n          message: '请输入有效的手机号',\r\n          trigger: ['blur']\r\n        });\r\n      }\r\n      \r\n      formRules[item.field] = rules;\r\n    }\r\n  });\r\n};\r\n\r\n// 获取选择框显示值\r\nconst getSelectDisplayValue = (field, options) => {\r\n  const value = formData[field];\r\n  if (!value || !options) return '';\r\n  \r\n  const option = options.find(opt => opt.value === value);\r\n  return option ? option.label : '';\r\n};\r\n\r\n// 打开选择器\r\nconst openPicker = (item) => {\r\n  if (!item.options || !Array.isArray(item.options)) {\r\n    uni.$u.toast('选项配置错误');\r\n    return;\r\n  }\r\n  \r\n  currentPickerField.value = item.field;\r\n  pickerColumns.value = [item.options];\r\n  pickerShow.value = true;\r\n};\r\n\r\n// 选择器确认\r\nconst onPickerConfirm = (e) => {\r\n  const { value } = e;\r\n  if (value && value[0] && currentPickerField.value) {\r\n    formData[currentPickerField.value] = value[0].value;\r\n  }\r\n  pickerShow.value = false;\r\n};\r\n\r\n// 显示确认弹窗\r\nconst handleSubmit = async () => {\r\n  if (!formRef.value) {\r\n    uni.$u.toast('表单初始化失败');\r\n    return;\r\n  }\r\n\r\n  try {\r\n    // 表单验证\r\n    await formRef.value.validate();\r\n\r\n    // 验证通过后显示确认弹窗\r\n    showConfirmModal.value = true;\r\n\r\n  } catch (error) {\r\n    console.error('表单验证失败:', error);\r\n    uni.$u.toast('请检查表单填写是否正确');\r\n  }\r\n};\r\n\r\n// 关闭确认弹窗\r\nconst closeConfirmModal = () => {\r\n  showConfirmModal.value = false;\r\n};\r\n\r\n// 确认提交报名\r\nconst confirmSubmitRegistration = async () => {\r\n  closeConfirmModal();\r\n\r\n  try {\r\n    isSubmitting.value = true;\r\n\r\n    // 提交数据\r\n    const response = await submitRegistrationApi({\r\n      eventId: eventId.value,\r\n      formData: formData\r\n    });\r\n\r\n    if (response.code === 200) {\r\n      uni.$u.toast('报名成功！');\r\n\r\n      // 🔄 状态分离重构：更新本地存储的报名状态，确保与新的分离模式兼容\r\n      try {\r\n        const registrationStatus = uni.getStorageSync('registrationStatus') || {};\r\n        registrationStatus[eventId.value] = {\r\n          isRegistered: true,\r\n          timestamp: Date.now(),\r\n          formData: formData, // 可选：保存表单数据供后续查看\r\n          source: 'user_registration' // 标记数据来源\r\n        };\r\n        uni.setStorageSync('registrationStatus', registrationStatus);\r\n        console.log('✅ 已在本地存储中标记报名状态（状态分离模式）:', registrationStatus);\r\n      } catch (error) {\r\n        console.warn('⚠️ 保存本地报名状态失败:', error);\r\n        // 即使本地存储失败，也不影响报名流程\r\n      }\r\n\r\n              // 🚀 【数据实时更新方案】发送全局数据变化事件\r\n        // 延迟跳转，让用户看到成功提示，然后发送全局广播\r\n        setTimeout(() => {\r\n          console.log('📤 发送数据变化广播事件...');\r\n\r\n          // 【关键步骤】发送全局事件，通知所有监听的页面数据已发生变化\r\n          uni.$emit('dataChanged');\r\n          console.log('✅ 已发送 dataChanged 事件');\r\n\r\n          // 返回到上一页（通常是活动详情页）\r\n          uni.navigateBack({\r\n            success: () => {\r\n              console.log('📄 返回上一页成功');\r\n\r\n              // 页面返回成功后，再次发送事件确保页面能收到\r\n              setTimeout(() => {\r\n                uni.$emit('dataChanged');\r\n                console.log('✅ 页面返回后再次发送 dataChanged 事件');\r\n              }, 100);\r\n            },\r\n            fail: (error) => {\r\n              console.warn('📄 页面返回失败:', error);\r\n              // 如果返回失败，跳转到首页\r\n              uni.switchTab({\r\n                url: '/pages/index/index'\r\n              });\r\n            }\r\n          });\r\n        }, 1500); // 让用户看到成功提示的时间\r\n\r\n    } else {\r\n      throw new Error(response.msg || '报名失败');\r\n    }\r\n\r\n  } catch (error) {\r\n    console.error('提交报名失败:', error);\r\n\r\n    if (error.message) {\r\n      uni.$u.toast(error.message);\r\n    } else {\r\n      uni.$u.toast('提交失败，请稍后重试');\r\n    }\r\n  } finally {\r\n    isSubmitting.value = false;\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.registration-page {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #F2F4FA;\r\n}\r\n\r\n.loading-container {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: #ffffff;\r\n  z-index: 999;\r\n}\r\n\r\n.login-prompt-container {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding-top: calc(var(--status-bar-height) + 44px); /* 状态栏高度 + 导航栏高度 */\r\n  min-height: 60vh;\r\n}\r\n\r\n.empty-container {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding-top: calc(var(--status-bar-height) + 44px); /* 状态栏高度 + 导航栏高度 */\r\n  min-height: 60vh;\r\n}\r\n\r\n.scroll-content {\r\n  flex: 1;\r\n  padding-top: calc(var(--status-bar-height) + 44px); /* 状态栏高度 + 导航栏高度 */\r\n  padding-bottom: 120rpx; /* 为底部按钮留出空间 */\r\n  box-sizing: border-box;\r\n}\r\n\r\n.event-title-section {\r\n  margin: 106rpx 30rpx 40rpx;\r\n  text-align: center;\r\n}\r\n\r\n.event-title {\r\n  width: 468rpx;\r\n    height: 44rpx;\r\n    font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\r\n    font-weight: normal;\r\n    font-size: 32rpx;\r\n    color: #23232A;\r\n    font-style: normal;\r\n    text-transform: none;\r\n    /* 建议增加 line-height 使文字垂直居中 */\r\n    line-height: 44rpx; \r\n}\r\n\r\n.form-container {\r\n\tdisplay: flex;\r\n\t  justify-content: center;\r\n  background-color: transparent;\r\n  margin: 0 30rpx 30rpx 30rpx;\r\n  padding: 30rpx;\r\n  border-radius: 20rpx;\r\n  box-shadow: none;\r\n}\r\n\r\n.bottom-spacer {\r\n  height: 40rpx;\r\n}\r\n\r\n\r\n\r\n.bottom-action-bar {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  width: 100%;\r\n  box-sizing: border-box;\r\n  z-index: 100;\r\n  \r\n  /*安全区域设置 */\r\n  height: calc(156rpx + env(safe-area-inset-bottom));\r\n  background-color: #FFFFFF;\r\n  border-top: 2rpx solid #EEEEEE;\r\n  border-radius: 0;\r\n  box-shadow: none;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  /* 设置左右内边距，并为底部安全区域留出空间 */\r\n  padding: 0 30rpx;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n/* uview-plus 组件样式覆盖 */\r\n:deep(.u-form-item__label) {\r\n  margin-bottom: 16rpx !important;\r\n}\r\n\r\n:deep(.u-input__content) {\r\n  min-height: 80rpx;\r\n}\r\n\r\n:deep(.u-textarea) {\r\n  background-color: transparent !important;\r\n  border-radius: 8rpx !important;\r\n}\r\n\r\n/* 报名确认弹窗样式 */\r\n.confirm-modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 9999;\r\n}\r\n\r\n.confirm-modal-content {\r\n  width: 654rpx;\r\n  height: 420rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 16rpx;\r\n  position: relative;\r\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.modal-header {\r\n  position: absolute;\r\n  top: 42rpx;\r\n  left: 48rpx;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .warning-icon {\r\n    width: 48rpx;\r\n    height: 40rpx;\r\n    margin-right: 16rpx;\r\n  }\r\n\r\n  .modal-title {\r\n    width: 142rpx;\r\n    height: 44rpx;\r\n    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\r\n    font-weight: normal;\r\n    font-size: 36rpx;\r\n    color: #23232A;\r\n    line-height: 44rpx;\r\n    text-align: center;\r\n    font-style: normal;\r\n    text-transform: none;\r\n  }\r\n}\r\n\r\n.modal-body {\r\n  position: absolute;\r\n  top: 158rpx;\r\n  left: 48rpx;\r\n\r\n  .modal-message {\r\n    width: 558rpx;\r\n    height: 44rpx;\r\n    font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\r\n    font-weight: normal;\r\n    font-size: 32rpx;\r\n    color: #23232A;\r\n    line-height: 44rpx;\r\n    text-align: left;\r\n    font-style: normal;\r\n    text-transform: none;\r\n  }\r\n}\r\n\r\n.modal-footer {\r\n  position: absolute;\r\n  top: 316rpx;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  display: flex;\r\n  gap: 24rpx;\r\n\r\n  .modal-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    transition: opacity 0.2s;\r\n\r\n    &:active {\r\n      opacity: 0.8;\r\n    }\r\n  }\r\n\r\n  .cancel-btn {\r\n      width: 292rpx;\r\n      height: 76rpx;\r\n      background: rgba(42, 97, 241, 0.1);\r\n      border-radius: 8rpx;\r\n      font-family: 'Alibaba PuHuiTi 3.0', 'Alibaba PuHuiTi 30';\r\n      font-weight: normal;\r\n      font-size: 28rpx;\r\n      color: #23232A;\r\n      line-height: 44rpx;\r\n  }\r\n\r\n  .confirm-btn {\r\n    width: 292rpx;\r\n    height: 76rpx;\r\n    background: #023F98;\r\n    border-radius: 8rpx;\r\n    font-family: 'Alibaba PuHuiTi 3.0', sans-serif;\r\n    font-size: 28rpx;\r\n    font-weight: 500;\r\n    color: #ffffff;\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages_sub/pages_event/registration.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "uni", "onLoad", "onShow", "getEventDetailApi", "getFormDefinitionApi", "submitRegistrationApi"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6PA,UAAM,UAAUA,cAAAA,IAAI,IAAI;AACxB,UAAM,YAAYA,cAAAA,IAAI,IAAI;AAC1B,UAAM,aAAaA,cAAAA,IAAI,CAAA,CAAE;AACzB,UAAM,WAAWC,cAAAA,SAAS,CAAA,CAAE;AAC5B,UAAM,YAAYA,cAAAA,SAAS,CAAA,CAAE;AAC7B,UAAM,YAAYD,cAAAA,IAAI,IAAI;AAC1B,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAG5B,UAAM,aAAaA,cAAAA,IAAI,KAAK;AAC5B,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAC5B,UAAM,qBAAqBA,cAAAA,IAAI,EAAE;AAGjC,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAGlC,UAAM,UAAUA,cAAG,IAAA;AACnB,UAAM,YAAYA,cAAG,IAAA;AAGrB,UAAM,mBAAmB,MAAM;AAC7B,YAAM,QAAQE,cAAAA,MAAI,eAAe,OAAO;AACxC,aAAO,CAAC,CAAC;AAAA,IACX;AAGAC,kBAAM,OAAC,OAAO,YAAY;AACxB,cAAQ,QAAQ,QAAQ;AAExB,UAAI,CAAC,QAAQ,OAAO;AAClBD,sBAAAA,MAAI,GAAG,MAAM,QAAQ;AACrB,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AACP;AAAA,MACD;AAAA,IAGH,CAAC;AAGDE,kBAAAA,OAAO,YAAY;AACjBF,oBAAAA,MAAA,MAAA,OAAA,iDAAY,wBAAwB;AAGpC,YAAM,qBAAqB;AAC3B,iBAAW,QAAQ;AAEnBA,oBAAA,MAAA,MAAA,OAAA,iDAAY,WAAW,kBAAkB;AACzCA,0BAAY,MAAA,OAAA,iDAAA,YAAYA,oBAAI,eAAe,OAAO,CAAC;AAEnD,UAAI,CAAC,oBAAoB;AAEvBA,sBAAAA,MAAA,MAAA,OAAA,iDAAY,4BAA4B;AACxC,kBAAU,QAAQ;AAElBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAGD,mBAAW,MAAM;AACfA,wBAAAA,oEAAY,iBAAiB;AAC7BA,wBAAAA,MAAI,aAAa;AAAA,YACf,MAAM,MAAM;AAEVA,4BAAAA,MAAA,MAAA,OAAA,iDAAY,YAAY;AACxBA,4BAAAA,MAAI,UAAU,EAAE,KAAK,qBAAsB,CAAA;AAAA,YAC5C;AAAA,UACT,CAAO;AAAA,QACF,GAAE,GAAG;AAAA,MAEV,OAAS;AAELA,sBAAAA,MAAY,MAAA,OAAA,iDAAA,qBAAqB;AAGjC,YAAI,QAAQ,OAAO;AACjB,gBAAM,cAAa;AAAA,QACpB;AAGD,YAAI,QAAQ,UAAU,CAAC,WAAW,SAAS,WAAW,MAAM,WAAW,IAAI;AACzE,gBAAM,mBAAkB;AAAA,QAC9B,OAAW;AAEL,oBAAU,QAAQ;AAAA,QACnB;AAAA,MACF;AAAA,IACH,CAAC;AAGD,UAAM,YAAY,MAAM;AACtBA,oBAAAA,MAAA,MAAA,OAAA,iDAAY,UAAU;AACtBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAY,MAAA,OAAA,iDAAA,oBAAoB;AAGhCA,oBAAAA,MAAI,aAAa;AAAA,QACf,SAAS,MAAM;AACbA,wBAAAA,MAAA,MAAA,OAAA,iDAAY,UAAU;AAAA,QACvB;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,6FAAa,cAAc,GAAG;AAG9BA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,YACL,SAAS,MAAM;AACbA,4BAAAA,MAAY,MAAA,OAAA,iDAAA,eAAe;AAAA,YAC5B;AAAA,YACD,MAAM,CAAC,SAAS;AACdA,iGAAa,mBAAmB,IAAI;AAGpCA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,KAAK;AAAA,gBACL,SAAS,MAAM;AACbA,gCAAAA,MAAY,MAAA,OAAA,iDAAA,WAAW;AAAA,gBACxB;AAAA,gBACD,MAAM,CAAC,SAAS;AACdA,gCAAc,MAAA,MAAA,SAAA,iDAAA,iBAAiB,IAAI;AACnCA,gCAAAA,MAAI,UAAU;AAAA,oBACZ,OAAO;AAAA,oBACP,MAAM;AAAA,kBACtB,CAAe;AAAA,gBACF;AAAA,cACb,CAAW;AAAA,YACF;AAAA,UACT,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,YAAY;AAChC,UAAI;AACF,cAAM,WAAW,MAAMG,eAAAA,kBAAkB,QAAQ,KAAK;AACtD,YAAI,SAAS,SAAS,OAAO,SAAS,MAAM;AAC1C,oBAAU,QAAQ,SAAS;AAC3BH,4FAAY,aAAa,UAAU,KAAK;AAAA,QAC9C,OAAW;AACL,gBAAM,IAAI,MAAM,SAAS,OAAO,UAAU;AAAA,QAC3C;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,iDAAA,aAAa,KAAK;AAAA,MAEjC;AAAA,IACH;AAGA,UAAM,qBAAqB,YAAY;AACrC,UAAI;AACF,kBAAU,QAAQ;AAElB,cAAM,WAAW,MAAMI,gCAAAA,qBAAqB,QAAQ,KAAK;AAEzD,YAAI,SAAS,SAAS,OAAO,SAAS,MAAM;AAE1C,cAAI;AAEJ,cAAI,OAAO,SAAS,SAAS,UAAU;AACrC,gBAAI;AAEF,+BAAiB,KAAK,MAAM,SAAS,IAAI;AACzCJ,4BAAA,MAAA,MAAA,OAAA,iDAAY,aAAa,cAAc;AAAA,YACxC,SAAQ,YAAY;AACnBA,4BAAA,MAAA,MAAA,SAAA,iDAAc,iBAAiB,UAAU;AACzC,oBAAM,IAAI,MAAM,UAAU;AAAA,YAC3B;AAAA,UACT,OAAa;AAEL,6BAAiB,SAAS;AAAA,UAC3B;AAGD,gBAAM,SAAS,eAAe,UAAU;AAExC,cAAI,MAAM,QAAQ,MAAM,KAAK,OAAO,SAAS,GAAG;AAC9C,uBAAW,QAAQ;AACnBA,0BAAY,MAAA,MAAA,OAAA,iDAAA,kBAAkB,OAAO,MAAM;AAC3C;AACA;UACR,OAAa;AACLA,+FAAa,qBAAqB,cAAc;AAChD,uBAAW,QAAQ;UACpB;AAAA,QACP,OAAW;AACL,gBAAM,IAAI,MAAM,SAAS,OAAO,UAAU;AAAA,QAC3C;AAAA,MAEF,SAAQ,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,iDAAA,aAAa,KAAK;AAChCA,sBAAG,MAAC,GAAG,MAAM,MAAM,WAAW,cAAc;AAC5C,mBAAW,QAAQ;MACvB,UAAY;AACR,kBAAU,QAAQ;AAAA,MACnB;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AACzB,iBAAW,MAAM,QAAQ,UAAQ;AAC/B,YAAI,KAAK,OAAO;AACd,mBAAS,KAAK,KAAK,IAAI,KAAK,gBAAgB;AAAA,QAC7C;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAC1B,iBAAW,MAAM,QAAQ,UAAQ;AAC/B,YAAI,KAAK,SAAS,KAAK,UAAU;AAC/B,gBAAM,QAAQ,CAAC;AAAA,YACb,UAAU;AAAA,YACV,SAAS,IAAI,KAAK,SAAS,WAAW,OAAO,IAAI,GAAG,KAAK,KAAK;AAAA,YAC9D,SAAS,CAAC,QAAQ,QAAQ;AAAA,UAClC,CAAO;AAGD,cAAI,KAAK,SAAS,SAAS;AACzB,kBAAM,KAAK;AAAA,cACT,SAAS;AAAA,cACT,SAAS;AAAA,cACT,SAAS,CAAC,MAAM;AAAA,YAC1B,CAAS;AAAA,UACT,WAAiB,KAAK,SAAS,SAAS;AAChC,kBAAM,KAAK;AAAA,cACT,SAAS;AAAA,cACT,SAAS;AAAA,cACT,SAAS,CAAC,MAAM;AAAA,YAC1B,CAAS;AAAA,UACF;AAED,oBAAU,KAAK,KAAK,IAAI;AAAA,QACzB;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,wBAAwB,CAAC,OAAO,YAAY;AAChD,YAAM,QAAQ,SAAS,KAAK;AAC5B,UAAI,CAAC,SAAS,CAAC;AAAS,eAAO;AAE/B,YAAM,SAAS,QAAQ,KAAK,SAAO,IAAI,UAAU,KAAK;AACtD,aAAO,SAAS,OAAO,QAAQ;AAAA,IACjC;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3B,UAAI,CAAC,KAAK,WAAW,CAAC,MAAM,QAAQ,KAAK,OAAO,GAAG;AACjDA,sBAAAA,MAAI,GAAG,MAAM,QAAQ;AACrB;AAAA,MACD;AAED,yBAAmB,QAAQ,KAAK;AAChC,oBAAc,QAAQ,CAAC,KAAK,OAAO;AACnC,iBAAW,QAAQ;AAAA,IACrB;AAGA,UAAM,kBAAkB,CAAC,MAAM;AAC7B,YAAM,EAAE,MAAO,IAAG;AAClB,UAAI,SAAS,MAAM,CAAC,KAAK,mBAAmB,OAAO;AACjD,iBAAS,mBAAmB,KAAK,IAAI,MAAM,CAAC,EAAE;AAAA,MAC/C;AACD,iBAAW,QAAQ;AAAA,IACrB;AAGA,UAAM,eAAe,YAAY;AAC/B,UAAI,CAAC,QAAQ,OAAO;AAClBA,sBAAAA,MAAI,GAAG,MAAM,SAAS;AACtB;AAAA,MACD;AAED,UAAI;AAEF,cAAM,QAAQ,MAAM;AAGpB,yBAAiB,QAAQ;AAAA,MAE1B,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,iDAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,GAAG,MAAM,aAAa;AAAA,MAC3B;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAC9B,uBAAiB,QAAQ;AAAA,IAC3B;AAGA,UAAM,4BAA4B,YAAY;AAC5C;AAEA,UAAI;AACF,qBAAa,QAAQ;AAGrB,cAAM,WAAW,MAAMK,sDAAsB;AAAA,UAC3C,SAAS,QAAQ;AAAA,UACjB;AAAA,QACN,CAAK;AAED,YAAI,SAAS,SAAS,KAAK;AACzBL,wBAAAA,MAAI,GAAG,MAAM,OAAO;AAGpB,cAAI;AACF,kBAAM,qBAAqBA,cAAG,MAAC,eAAe,oBAAoB,KAAK,CAAA;AACvE,+BAAmB,QAAQ,KAAK,IAAI;AAAA,cAClC,cAAc;AAAA,cACd,WAAW,KAAK,IAAK;AAAA,cACrB;AAAA;AAAA,cACA,QAAQ;AAAA;AAAA,YAClB;AACQA,0BAAAA,MAAI,eAAe,sBAAsB,kBAAkB;AAC3DA,0BAAY,MAAA,MAAA,OAAA,iDAAA,4BAA4B,kBAAkB;AAAA,UAC3D,SAAQ,OAAO;AACdA,0BAAa,MAAA,MAAA,QAAA,iDAAA,kBAAkB,KAAK;AAAA,UAErC;AAIC,qBAAW,MAAM;AACfA,0BAAAA,MAAA,MAAA,OAAA,iDAAY,kBAAkB;AAG9BA,gCAAI,MAAM,aAAa;AACvBA,0BAAAA,oEAAY,sBAAsB;AAGlCA,0BAAAA,MAAI,aAAa;AAAA,cACf,SAAS,MAAM;AACbA,8BAAAA,MAAA,MAAA,OAAA,iDAAY,YAAY;AAGxB,2BAAW,MAAM;AACfA,sCAAI,MAAM,aAAa;AACvBA,gCAAAA,MAAA,MAAA,OAAA,iDAAY,4BAA4B;AAAA,gBACzC,GAAE,GAAG;AAAA,cACP;AAAA,cACD,MAAM,CAAC,UAAU;AACfA,8BAAA,MAAA,MAAA,QAAA,iDAAa,cAAc,KAAK;AAEhCA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,KAAK;AAAA,gBACrB,CAAe;AAAA,cACF;AAAA,YACb,CAAW;AAAA,UACF,GAAE,IAAI;AAAA,QAEf,OAAW;AACL,gBAAM,IAAI,MAAM,SAAS,OAAO,MAAM;AAAA,QACvC;AAAA,MAEF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,iDAAc,WAAW,KAAK;AAE9B,YAAI,MAAM,SAAS;AACjBA,wBAAAA,MAAI,GAAG,MAAM,MAAM,OAAO;AAAA,QAChC,OAAW;AACLA,wBAAAA,MAAI,GAAG,MAAM,YAAY;AAAA,QAC1B;AAAA,MACL,UAAY;AACR,qBAAa,QAAQ;AAAA,MACtB;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACznBA,GAAG,WAAW,eAAe;"}