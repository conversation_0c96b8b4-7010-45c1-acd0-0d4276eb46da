<template>
  <view v-if="!loading && countryList.length > 0" class="highlight-container">
    <view class="section-header">
      <text class="title-main">出海</text>
      <text class="title-gradient">国别</text>
    </view>

    <scroll-view class="country-selector-scroll" scroll-x :show-scrollbar="false">
      <view class="country-selector-inner">
        <view
            v-for="country in countryList"
            :key="country.id"
            class="country-card"
            :class="{ active: selectedCountryId === country.id }"
            @click="selectCountry(country.id)"
        >
          <image class="country-card-bg" :src="baseUrl + country.listCoverUrl" mode="aspectFill"></image>

          <view class="corner-badge" :class="{ gold: selectedCountryId === country.id, blue: selectedCountryId !== country.id }">
            <text class="badge-text">{{ country.nameCn }}</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <view class="tabs-wrapper">
      <scroll-view class="tabs" scroll-x="true" :show-scrollbar="false">
        <view
            v-for="tab in tabs"
            :key="tab.id"
            class="tab-item"
            :class="{ active: activeTabId === tab.id }"
            @click="onTabClick(tab.id)"
        >
          <image class="tab-icon" :src="activeTabId === tab.id ? tab.activeIcon : tab.icon"></image>
          <text class="tab-text">{{ tab.name }}</text>
        </view>
      </scroll-view>
    </view>

    <view v-if="detailLoading" class="content-loading">
      <uni-load-more status="loading" />
    </view>
    <view v-else-if="selectedCountryDetails" class="content-display-area">
      <view class="content-header">
        <text class="content-title">{{ selectedContentTitle }}</text>
        <view class="more-link" @click="navigateToDetailWithTab">
          <text>更多</text>
          <uni-icons type="right" size="14" color="#888"></uni-icons>
        </view>
      </view>
      <view class="summary-content">
        <rich-text :nodes="selectedContent"></rich-text>
      </view>
    </view>
  </view>
</template>

<script setup>
// script 部分无需修改，保持原样
import { ref, computed, onMounted } from 'vue';
import { getCountryList, getCountryDetail } from '@/api/content/country.js';
import { IMAGE_BASE_URL } from '@/utils/config.js';

const baseUrl = IMAGE_BASE_URL;

const loading = ref(true);
const detailLoading = ref(false);
const countryList = ref([]);
const selectedCountryId = ref(null);
const selectedCountryDetails = ref(null);
const activeTabId = ref('basic');

const tabs = ref([
  { id: 'basic', name: '基本信息', icon: '/static/icons/基本信息icon浅色@2x.png', activeIcon: '/static/icons/基本信息icon深色@2x.png' },
  { id: 'investment', name: '招商政策', icon: '/static/icons/招商政策icon浅色@2x.png', activeIcon: '/static/icons/招商政策icon深色@2x.png' },
  { id: 'customs', name: '海关政策', icon: '/static/icons/海关icon浅色@2x.png', activeIcon: '/static/icons/海关icon深色@2x.png' },
  { id: 'tax', name: '税务政策', icon: '/static/icons/税务icon浅色@2x.png', activeIcon: '/static/icons/税务icon深色@2x.png' },
  { id: 'parks', name: '工业园区', icon: '/static/icons/工业园区icon浅色@2x.png', activeIcon: '/static/icons/工业园区icon深色@2x.png' },
]);

const selectedContentTitle = computed(() => {
  const countryName = selectedCountryDetails.value?.nameCn || '';
  const tabName = tabs.value.find(t => t.id === activeTabId.value)?.name || '';
  return `${countryName} - ${tabName}`;
});

const selectedContent = computed(() => {
  if (!selectedCountryDetails.value) return '<p>暂无相关信息。</p>';
  const contentMapping = {
    basic: selectedCountryDetails.value.introduction,
    investment: selectedCountryDetails.value.investmentPolicy,
    customs: selectedCountryDetails.value.customsPolicy,
    tax: selectedCountryDetails.value.taxPolicy,
    parks: '<p>请点击“更多”查看详细的工业园区列表。</p>'
  };
  return contentMapping[activeTabId.value] || '<p>暂无相关信息。</p>';
});

const fetchCountryDetails = async (id) => {
  detailLoading.value = true;
  selectedCountryDetails.value = null;
  try {
    const res = await getCountryDetail(id);
    selectedCountryDetails.value = res.data;
  } catch (error) {
    console.error(`获取ID为 ${id} 的国别详情失败:`, error);
  } finally {
    detailLoading.value = false;
  }
};

const fetchFeaturedCountries = async () => {
  loading.value = true;
  try {
    const params = { pageNum: 1, pageSize: 5 };
    const res = await getCountryList(params);
    if (res.data && res.data.length > 0) {
      countryList.value = res.data;
      const firstCountryId = res.data[0].id;
      selectedCountryId.value = firstCountryId;
      await fetchCountryDetails(firstCountryId);
    }
  } catch (error) {
    console.error('获取推荐国别失败:', error);
  } finally {
    loading.value = false;
  }
};

const selectCountry = (id) => {
  if (selectedCountryId.value === id) return;
  selectedCountryId.value = id;
  activeTabId.value = 'basic';
  fetchCountryDetails(id);
};

const onTabClick = (tabId) => {
  activeTabId.value = tabId;
};

const navigateToDetailWithTab = () => {
  if (selectedCountryId.value) {
    uni.navigateTo({
      url: `/pages_sub/pages_country/detail?id=${selectedCountryId.value}&tab=${activeTabId.value}`
    });
  }
};

onMounted(() => {
  fetchFeaturedCountries();
});
</script>

<style lang="scss" scoped>
/* 整体容器 */
.highlight-container {
  margin: 24rpx 0;
  padding: 32rpx;
  border-radius: 20rpx;
  background: linear-gradient(to bottom, rgba(2, 63, 152, 0.1), rgba(2, 63, 152, 0));
}

/* 标题 */
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}
.title-main {
  font-size: 40rpx;
  font-weight: bold;
  color: #023F98;
}
.title-gradient {
  font-size: 40rpx;
  font-weight: bold;
  background-image: linear-gradient(to right, #FFAD22, #FFBB87);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* 国家选择器 */
.country-selector-scroll {
  width: 100%;
  padding-bottom: 24rpx;
}
.country-selector-inner {
  display: flex;
  flex-direction: row;
}

.country-card {
  position: relative;
  flex-shrink: 0;
  width: 280rpx;
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
  margin-right: 24rpx;
  border: 4rpx solid #FFFFFF;
  transition: all 0.3s ease;

  &.active {
    /* 最简单稳定的方案：直接用粗边框 + box-shadow */
    border: 10rpx solid #FFBF51;
    box-shadow: 0 0 0 2rpx #FFD700;
    transform: scale(1.05);
  }
}

.country-card-bg {
  width: 100%;
  height: 100%;
}

.corner-badge {
  position: absolute;
  top: 0;
  left: 0;
  width: 150rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: 100% 100%;

  /* 核心修改3：提高角标的层级，确保它在图片边框之上 */
  z-index: 2;

  &.gold {
    background-image: url('http://**************:9000/hongda-public/system%2FFrame%201_slices%2F%E9%87%91%E8%89%B2%E8%A7%92%E6%A0%87%402x.png');
    .badge-text {
      color: #23232A;
    }
  }
  &.blue {
    background-image: url('http://**************:9000/hongda-public/system%2FFrame%201_slices%2F%E8%93%9D%E8%89%B2%E8%A7%92%E6%A0%87%402x.png');
    .badge-text {
      color: #FFFFFF;
    }
  }
}
.badge-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* Tab 标签栏及以下样式保持不变 */
.tabs-wrapper {
  background-color: #F4F4F4;
  border-radius: 16rpx;
  padding: 12rpx;
}
.tabs {
  display: flex;
  white-space: nowrap;
}
.tab-item {
  display: inline-flex;
  align-items: center;
  gap: 12rpx;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
  &:last-child { margin-right: 0; }
  .tab-icon { width: 40rpx; height: 40rpx; }
  .tab-text { font-size: 28rpx; color: #9B9A9A; }
  &.active {
    background-image: url('http://**************:9000/hongda-public/system%2FFrame%201_slices%2F%E9%87%91%E8%89%B2%E8%A7%92%E6%A0%87%402x.png');
    background-size: cover;
    background-position: center;
    .tab-text { color: #23232A; font-weight: bold; }
  }
}

.content-loading {
  padding: 40rpx 0;
}
.content-display-area {
  margin-top: 32rpx;
}
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.content-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.more-link {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #888;
}
.summary-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.7;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
</style>