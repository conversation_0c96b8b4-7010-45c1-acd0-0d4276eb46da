"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_data_user = require("../../api/data/user.js");
if (!Array) {
  const _easycom_up_avatar2 = common_vendor.resolveComponent("up-avatar");
  const _easycom_up_cell2 = common_vendor.resolveComponent("up-cell");
  const _easycom_up_cell_group2 = common_vendor.resolveComponent("up-cell-group");
  (_easycom_up_avatar2 + _easycom_up_cell2 + _easycom_up_cell_group2)();
}
const _easycom_up_avatar = () => "../../uni_modules/uview-plus/components/u-avatar/u-avatar.js";
const _easycom_up_cell = () => "../../uni_modules/uview-plus/components/u-cell/u-cell.js";
const _easycom_up_cell_group = () => "../../uni_modules/uview-plus/components/u-cell-group/u-cell-group.js";
if (!Math) {
  (_easycom_up_avatar + _easycom_up_cell + _easycom_up_cell_group + CustomTabBar)();
}
const CustomTabBar = () => "../../components/layout/CustomTabBar.js";
const _sfc_main = /* @__PURE__ */ Object.assign({
  name: "ProfileIndex"
  // 页面名称与 pages.json 路径一致
}, {
  __name: "index",
  setup(__props) {
    const isLoggedIn = common_vendor.ref(false);
    const userInfo = common_vendor.ref(null);
    const getUserDisplayName = common_vendor.computed(() => {
      if (!userInfo.value) {
        return "用户";
      }
      if (userInfo.value.nickname) {
        return userInfo.value.nickname;
      }
      if (userInfo.value.phoneNumber) {
        const phone = userInfo.value.phoneNumber;
        if (phone.length === 11) {
          return phone.substring(0, 3) + "****" + phone.substring(7);
        }
        return phone;
      }
      return "用户";
    });
    const getPhoneDisplay = common_vendor.computed(() => {
      var _a, _b;
      if (!isLoggedIn.value) {
        return "请先登录";
      }
      const phone = ((_a = userInfo.value) == null ? void 0 : _a.phone) || ((_b = userInfo.value) == null ? void 0 : _b.phoneNumber);
      if (!phone) {
        return "未绑定";
      }
      if (phone.length === 11) {
        return phone.substring(0, 3) + "****" + phone.substring(7);
      }
      return phone;
    });
    const handleNavigate = (url) => {
      if (!isLoggedIn.value) {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:149", "用户未登录，跳转到登录页");
        goToLogin();
        return;
      }
      common_vendor.index.__f__("log", "at pages/profile/index.vue:155", "用户已登录，跳转到:", url);
      common_vendor.index.navigateTo({
        url,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/profile/index.vue:159", "页面跳转失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    };
    const handleOrderCardClick = () => {
      if (!isLoggedIn.value) {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:172", "点击报名订单卡片，需要先登录");
        goToLogin();
      } else {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:175", "点击报名订单卡片，跳转到订单页面");
        common_vendor.index.navigateTo({
          url: "/pages_sub/pages_profile/orders",
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/profile/index.vue:179", "跳转订单页面失败:", err);
            common_vendor.index.showToast({
              title: "页面跳转失败",
              icon: "none",
              duration: 2e3
            });
          }
        });
      }
    };
    const handleBindPhoneClick = () => {
      if (!isLoggedIn.value) {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:193", "绑定手机号需要先登录");
        goToLogin();
        return;
      }
      common_vendor.index.__f__("log", "at pages/profile/index.vue:199", "跳转到绑定手机号页面");
      common_vendor.index.navigateTo({
        url: "/pages/profile/bind-phone",
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/profile/index.vue:203", "跳转绑定手机号页面失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    };
    const handleDeleteAccountClick = () => {
      if (!isLoggedIn.value) {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:216", "注销账号需要先登录");
        goToLogin();
        return;
      }
      common_vendor.index.showModal({
        title: "注销账号",
        content: "注销账号将删除您的所有数据，此操作不可恢复，确定要继续吗？",
        confirmText: "确定注销",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.__f__("log", "at pages/profile/index.vue:230", "用户确认注销账号");
            confirmDeleteAccount();
          }
        }
      });
    };
    const confirmDeleteAccount = async () => {
      common_vendor.index.__f__("log", "at pages/profile/index.vue:239", "=== 开始执行注销账号流程 ===");
      common_vendor.index.showLoading({ title: "正在注销..." });
      try {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:245", "调用后端注销接口...");
        const result = await api_data_user.deleteAccountApi();
        common_vendor.index.__f__("log", "at pages/profile/index.vue:248", "注销接口调用成功:", result);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "账号已成功注销",
          icon: "success",
          duration: 2e3
        });
        logout(true);
        common_vendor.index.__f__("log", "at pages/profile/index.vue:263", "注销账号流程完成");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/profile/index.vue:266", "注销账号过程中发生错误:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "注销失败，请稍后再试",
          icon: "none",
          duration: 3e3
        });
        common_vendor.index.__f__("error", "at pages/profile/index.vue:279", "注销失败详情:", {
          message: error.message,
          stack: error.stack
        });
      }
    };
    const checkLoginStatus = () => {
      const token = common_vendor.index.getStorageSync("token");
      const userInfoData = common_vendor.index.getStorageSync("userInfo");
      common_vendor.index.__f__("log", "at pages/profile/index.vue:292", "从本地存储获取的token:", token);
      common_vendor.index.__f__("log", "at pages/profile/index.vue:293", "从本地存储获取的userInfo:", userInfoData);
      const newLoginStatus = !!token;
      common_vendor.index.__f__("log", "at pages/profile/index.vue:296", "计算出的登录状态:", newLoginStatus);
      common_vendor.index.__f__("log", "at pages/profile/index.vue:297", "当前页面登录状态:", isLoggedIn.value);
      isLoggedIn.value = newLoginStatus;
      userInfo.value = userInfoData || null;
      if (isLoggedIn.value && userInfoData) {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:305", "用户已登录，用户信息:", userInfoData);
        if (userInfoData.phoneNumber) {
          common_vendor.index.__f__("log", "at pages/profile/index.vue:307", "用户手机号:", userInfoData.phoneNumber);
        }
      } else if (isLoggedIn.value && !userInfoData) {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:310", "有token但无用户信息");
      } else {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:312", "用户未登录");
      }
    };
    const goToLogin = () => {
      common_vendor.index.navigateTo({
        url: "/pages_sub/pages_other/login"
      });
    };
    const logout = (isDeleteAccount = false) => {
      if (isDeleteAccount) {
        clearUserData("已退出登录");
        return;
      }
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            clearUserData("已退出登录");
          }
        }
      });
    };
    const handleEditNickname = () => {
      var _a;
      if (!isLoggedIn.value) {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:346", "编辑昵称需要先登录");
        goToLogin();
        return;
      }
      common_vendor.index.showModal({
        title: "修改昵称",
        editable: true,
        placeholderText: "请输入新昵称",
        content: ((_a = userInfo.value) == null ? void 0 : _a.nickname) || "",
        success: async (res) => {
          if (res.confirm && res.content && res.content.trim()) {
            const newNickname = res.content.trim();
            if (newNickname.length > 20) {
              common_vendor.index.showToast({
                title: "昵称不能超过20个字符",
                icon: "none",
                duration: 2e3
              });
              return;
            }
            await updateNickname(newNickname);
          }
        }
      });
    };
    const updateNickname = async (newNickname) => {
      common_vendor.index.__f__("log", "at pages/profile/index.vue:380", "=== 开始更新昵称 ===");
      common_vendor.index.showLoading({ title: "正在更新..." });
      try {
        common_vendor.index.__f__("log", "at pages/profile/index.vue:386", "调用updateUserInfoApi更新昵称:", newNickname);
        const result = await api_data_user.updateUserInfoApi({
          nickname: newNickname
        });
        common_vendor.index.__f__("log", "at pages/profile/index.vue:391", "昵称更新接口调用成功:", result);
        common_vendor.index.hideLoading();
        if (userInfo.value) {
          userInfo.value.nickname = newNickname;
          common_vendor.index.setStorageSync("userInfo", userInfo.value);
        }
        common_vendor.index.showToast({
          title: "昵称修改成功",
          icon: "success",
          duration: 2e3
        });
        common_vendor.index.__f__("log", "at pages/profile/index.vue:410", "昵称更新完成");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/profile/index.vue:413", "更新昵称过程中发生错误:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: error.message || "更新失败，请稍后再试",
          icon: "none",
          duration: 3e3
        });
        common_vendor.index.__f__("error", "at pages/profile/index.vue:425", "昵称更新失败详情:", {
          message: error.message,
          stack: error.stack
        });
      }
    };
    const clearUserData = (message) => {
      common_vendor.index.__f__("log", "at pages/profile/index.vue:434", "=== 开始清除用户数据 ===");
      common_vendor.index.removeStorageSync("token");
      common_vendor.index.removeStorageSync("userInfo");
      isLoggedIn.value = false;
      userInfo.value = null;
      if (message) {
        common_vendor.index.showToast({
          title: message,
          icon: "success",
          duration: 1500
        });
      }
      common_vendor.index.__f__("log", "at pages/profile/index.vue:453", "用户数据已清除，UI已更新");
    };
    common_vendor.onShow(() => {
      common_vendor.index.hideTabBar();
      setTimeout(() => {
        checkLoginStatus();
      }, 100);
    });
    common_vendor.onLoad(() => {
      checkLoginStatus();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$2,
        b: common_vendor.o(($event) => !isLoggedIn.value ? goToLogin : null),
        c: common_vendor.p({
          size: 54,
          src: isLoggedIn.value && userInfo.value && userInfo.value.avatarUrl ? userInfo.value.avatarUrl : "/static/profile/default-avatar.png"
        }),
        d: isLoggedIn.value
      }, isLoggedIn.value ? {
        e: common_vendor.t(getUserDisplayName.value),
        f: common_assets._imports_1$1,
        g: common_vendor.o(handleEditNickname)
      } : {
        h: common_vendor.o(goToLogin)
      }, {
        i: common_assets._imports_2$1,
        j: common_assets._imports_3$1,
        k: common_vendor.o(handleOrderCardClick),
        l: common_assets._imports_4,
        m: common_vendor.o(handleBindPhoneClick),
        n: common_vendor.p({
          title: "绑定手机号",
          value: getPhoneDisplay.value,
          isLink: false,
          ["arrow-direction"]: "right",
          border: false
        }),
        o: common_assets._imports_5,
        p: common_vendor.o(($event) => handleNavigate("/pages/static/privacy")),
        q: common_vendor.p({
          title: "隐私政策",
          isLink: true,
          ["arrow-direction"]: "right",
          border: false
        }),
        r: common_assets._imports_6,
        s: common_vendor.o(($event) => handleNavigate("/pages/static/agreement")),
        t: common_vendor.p({
          title: "用户协议",
          isLink: true,
          ["arrow-direction"]: "right",
          border: false
        }),
        v: common_assets._imports_7,
        w: common_vendor.o(handleDeleteAccountClick),
        x: common_vendor.p({
          title: "注销账号",
          isLink: true,
          ["arrow-direction"]: "right",
          border: false
        }),
        y: common_vendor.p({
          border: false
        }),
        z: isLoggedIn.value
      }, isLoggedIn.value ? {
        A: common_vendor.o(logout)
      } : {}, {
        B: common_vendor.p({
          current: 4
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-201c0da5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/index.js.map
