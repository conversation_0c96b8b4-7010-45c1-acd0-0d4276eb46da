import config from '@/utils/config.js';

export function getFullImageUrl(relativePath) {
	if (!relativePath) {
		// 为活动图标提供默认值，使用现有的蓝色logo作为默认图标
		return '/static/event/美妆logo蓝@2x.png';
	}
	// 如果是完整的URL，直接返回。不要强制修改协议！
	if (relativePath.startsWith('http')) {
		return relativePath;
	}
	// 如果是相对路径，则拼接
	const baseUrl = config.imageBaseUrl || '';
	const path = relativePath.startsWith('/') ? relativePath.substring(1) : relativePath;
	return `${baseUrl}/${path}`;
}