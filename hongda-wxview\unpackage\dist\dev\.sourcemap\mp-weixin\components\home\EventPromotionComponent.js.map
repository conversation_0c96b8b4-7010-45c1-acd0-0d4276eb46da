{"version": 3, "file": "EventPromotionComponent.js", "sources": ["components/home/<USER>", "../../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYWxsIGNvZGUvaG9uZ2RhLXd4dmlldy9ob25nZGEtd3h2aWV3L2NvbXBvbmVudHMvaG9tZS9FdmVudFByb21vdGlvbkNvbXBvbmVudC52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"promo-container\">\r\n\t\t<!-- 调试信息 - 开发时使用 -->\r\n\t<!-- \t<view v-if=\"true\" class=\"debug-info\" style=\"padding: 10rpx; background: #f0f0f0; margin-bottom: 10rpx; font-size: 24rpx;\">\r\n\t\t\t<text>isLoading: {{ isLoading }}</text><br/>\r\n\t\t\t<text>promoDataList.length: {{ promoDataList.length }}</text><br/>\r\n\t\t\t<text>currentIndex: {{ currentIndex }}</text>\r\n\t\t</view> -->\r\n\t\t\r\n\t\t<!-- 骨架屏 - 加载时显示 -->\r\n\t\t<view v-if=\"isLoading\" class=\"skeleton-wrapper\">\r\n\t\t\t<u-skeleton\r\n\t\t\t\t:loading=\"true\"\r\n\t\t\t\t:animate=\"true\"\r\n\t\t\t\t:rows=\"4\"\r\n\t\t\t\t:title=\"true\"\r\n\t\t\t\ttitleWidth=\"60%\"\r\n\t\t\t\trowsWidth=\"['100%', '40%', '40%', '100%']\"\r\n\t\t\t\trowsHeight=\"['180px', '20px', '20px', '40px']\"\r\n\t\t\t></u-skeleton>\r\n\t\t</view>\r\n\r\n\t\t<!-- 轮播卡片容器 - 有数据时显示 -->\r\n\t\t<view v-else-if=\"promoDataList.length > 0\" class=\"promo-card-wrapper\">\r\n\t\t\t<!-- 轮播区域 -->\r\n\t\t\t<swiper \r\n\t\t\t\tref=\"swiperRef\"\r\n\t\t\t\tclass=\"promo-swiper\"\r\n\t\t\t\t:indicator-dots=\"false\"\r\n\t\t\t\t:autoplay=\"true\"\r\n\t\t\t\t:interval=\"4000\"\r\n\t\t\t\t:duration=\"500\"\r\n\t\t\t\t:circular=\"true\"\r\n\t\t\t\t:current=\"currentIndex\"\r\n\t\t\t\t@change=\"onSwiperChange\"\r\n\t\t\t>\r\n\t\t\t\t<swiper-item \r\n\t\t\t\t\tv-for=\"(item, index) in promoDataList\" \r\n\t\t\t\t\t:key=\"item.id || index\"\r\n\t\t\t\t\tclass=\"swiper-item\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<view class=\"promo-card\" @click=\"handlePromoClick(item)\">\r\n\t\t\t\t\t\t<!-- 1. 顶部标题 -->\r\n\t\t\t\t\t\t<view class=\"promo-header\">\r\n\t\t\t\t\t\t\t<image\r\n\t\t\t\t\t\t\t\t:src=\"item.iconUrl || '/static/event/美妆logo蓝@2x.png'\"\r\n\t\t\t\t\t\t\t\tclass=\"promo-icon\"\r\n\t\t\t\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t\t\t\t@error=\"onIconError\"\r\n\t\t\t\t\t\t\t\t@load=\"onIconLoad\"\r\n\t\t\t\t\t\t\t></image>\r\n\t\t\t\t\t\t\t<text class=\"promo-title\">{{ item.title }}</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 2. 中间主图 -->\r\n\t\t\t\t\t\t<image \r\n\t\t\t\t\t\t\tclass=\"promo-main-image\" \r\n\t\t\t\t\t\t\t:src=\"item.image\" \r\n\t\t\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t\t\t:lazy-load=\"true\"\r\n\t\t\t\t\t\t\t@error=\"onImageError\"\r\n\t\t\t\t\t\t\t@load=\"onImageLoad\"\r\n\t\t\t\t\t\t></image>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<!-- 3. 简介信息 -->\r\n\t\t\t\t\t\t<view class=\"promo-description\">\r\n\t\t\t\t\t\t\t<view v-if=\"item.descriptionLine1\" class=\"desc-item\">\r\n\t\t\t\t\t\t\t\t<image class=\"desc-icon\" src=\"/static/EventPromotion/flame-icon.png\"></image>\r\n\t\t\t\t\t\t\t\t<text class=\"desc-text\">{{ item.descriptionLine1 }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view v-if=\"item.descriptionLine2\" class=\"desc-item\">\r\n\t\t\t\t\t\t\t\t<image class=\"desc-icon\" src=\"/static/EventPromotion/thumb-up-icon.png\"></image>\r\n\t\t\t\t\t\t\t\t<text class=\"desc-text\">{{ item.descriptionLine2 }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<!-- 4. 底部按钮 -->\r\n\t\t\t\t\t\t<view class=\"promo-footer\">\r\n\t\t\t\t\t\t\t<up-button \r\n\t\t\t\t\t\t\t\ttype=\"primary\" \r\n\t\t\t\t\t\t\t\tshape=\"square\" \r\n\t\t\t\t\t\t\t\ttext=\"立即报名\"\r\n\t\t\t\t\t\t\t\tsize=\"large\"\r\n\t\t\t\t\t\t\t\t:customStyle=\"{ \r\n\t\t\t\t\t\t\t\t\tbackgroundColor: '#0052D9', /*蓝色 */\r\n\t\t\t\t\t\t\t\t\theight: '70rpx'\r\n\t\t\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t\t></up-button>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t\t\r\n\t\t\t<!-- 自定义指示器 - 放在立即报名按钮下方 -->\r\n\t\t\t<view v-if=\"promoDataList.length > 1\" class=\"custom-indicators\">\r\n\t\t\t\t<view \r\n\t\t\t\t\tv-for=\"(item, index) in promoDataList\" \r\n\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\tclass=\"indicator-dot\"\r\n\t\t\t\t\t:class=\"{ 'active': currentIndex === index }\"\r\n\t\t\t\t\t@click=\"switchToSlide(index)\"\r\n\t\t\t\t></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 无数据时显示提示 -->\r\n\t\t<view v-else class=\"no-data-tip\" style=\"padding: 40rpx; text-align: center; color: #999;\">\r\n\t\t\t<text>暂无推广活动</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue';\r\nimport { getAdListByPositionApi } from '@/api/platform/ad.js';\r\nimport { getFullImageUrl } from '@/utils/image.js';\r\n\r\n// --- 响应式状态 ---\r\n// 改为数组，支持多个推广数据\r\nconst promoDataList = ref([]); \r\nconst isLoading = ref(true);\r\nconst currentIndex = ref(0); // 当前轮播索引\r\n\r\n// 广告位代码常量\r\nconst POSITION_CODE = 'HOME_PROMO_EVENT';\r\n\r\n// --- 方法 ---\r\n\r\n/**\r\n * 获取推广活动数据\r\n */\r\nconst fetchPromoData = async () => {\r\n\ttry {\r\n\t\t// 获取多条数据支持轮播\r\n\t\tconst response = await getAdListByPositionApi(POSITION_CODE, {\r\n\t\t\tpageSize: 10\r\n\t\t});\r\n\t\t\r\n\t\tconsole.log('活动推广API响应:', response);\r\n\r\n\t\t// 详细检查每个广告数据的iconUrl字段\r\n\t\tif (response.data && Array.isArray(response.data)) {\r\n\t\t\tresponse.data.forEach((ad, index) => {\r\n\t\t\t\tconsole.log(`广告${index + 1}的iconUrl:`, ad.iconUrl);\r\n\t\t\t});\r\n\t\t}\r\n\t\t\r\n\t\tif (response.data && Array.isArray(response.data) && response.data.length > 0) {\r\n\t\t\t// 处理所有返回的广告数据\r\n\t\t\tpromoDataList.value = response.data.map(ad => {\r\n\r\n\t\t\t\tconst processedData = {\r\n\t\t\t\t\tid: ad.id,\r\n\t\t\t\t\ttitle: ad.title,\r\n\t\t\t\t\timage: getFullImageUrl(ad.imageUrl),\r\n\t\t\t\t\ticonUrl: getFullImageUrl(ad.iconUrl), // 修复：使用getFullImageUrl处理图标URL\r\n\t\t\t\t\tlinkUrl: ad.finalLinkUrl || ad.linkUrl, // 优先使用最终跳转链接\r\n\t\t\t\t\t// 使用后端返回的活动简介和卖点字段，如果为空则提供默认文案\r\n\t\t\t\t\tdescriptionLine1: ad.eventSummary || '官方认证，品质保证',\r\n\t\t\t\t\tdescriptionLine2: ad.eventSellPoint || '干货满满，不容错过'\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// 调试信息：检查图标URL\r\n\t\t\t\tconsole.log('活动推广图标调试:', {\r\n\t\t\t\t\t原始iconUrl: ad.iconUrl,\r\n\t\t\t\t\t处理后iconUrl: processedData.iconUrl,\r\n\t\t\t\t\ttitle: ad.title\r\n\t\t\t\t});\r\n\r\n\t\t\t\treturn processedData;\r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\tconsole.warn('未找到推广数据或返回格式异常:', response);\r\n\t\t\tpromoDataList.value = []; // 确保清空数据\r\n\t\t}\r\n\t\t\r\n\t} catch (error) {\r\n\t\tconsole.error('获取推广数据失败:', error);\r\n\t} finally {\r\n\t\tisLoading.value = false;\r\n\t}\r\n};\r\n\r\n/**\r\n * 轮播切换事件处理\r\n */\r\nconst onSwiperChange = (e) => {\r\n\tcurrentIndex.value = e.detail.current;\r\n\t// console.log('轮播切换到索引:', currentIndex.value);\r\n};\r\n\r\n/**\r\n * 点击指示器切换轮播\r\n */\r\nconst switchToSlide = (index) => {\r\n\tcurrentIndex.value = index;\r\n\tconsole.log('点击指示器切换到索引:', index);\r\n};\r\n\r\n/**\r\n * 处理卡片点击事件\r\n */\r\nconst handlePromoClick = (promoItem) => {\r\n\tif (promoItem && promoItem.linkUrl) {\r\n\t\tconst linkUrl = promoItem.linkUrl;\r\n\t\tconsole.log('准备跳转到:', linkUrl);\r\n\t\t\r\n\t\tif (linkUrl.startsWith('http')) {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/webview/index?url=${encodeURIComponent(linkUrl)}&title=${encodeURIComponent(promoItem.title || '详情')}`\r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: linkUrl\r\n\t\t\t});\r\n\t\t}\r\n\t} else {\r\n\t\tconsole.warn('推广卡片跳转链接为空');\r\n\t}\r\n};\r\n\r\n/**\r\n * 图片加载成功事件\r\n */\r\nconst onImageLoad = (e) => {\r\n\tconsole.log('图片加载成功');\r\n};\r\n\r\n/**\r\n * 图片加载失败事件\r\n */\r\nconst onImageError = (e) => {\r\n\tconsole.error('图片加载失败:', e);\r\n};\r\n\r\n/**\r\n * 图标加载成功事件\r\n */\r\nconst onIconLoad = (e) => {\r\n\tconsole.log('图标加载成功');\r\n};\r\n\r\n/**\r\n * 图标加载失败事件\r\n */\r\nconst onIconError = (e) => {\r\n\tconsole.error('图标加载失败:', e);\r\n};\r\n\r\n// --- 生命周期 ---\r\nonMounted(() => {\r\n\tfetchPromoData();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.promo-container {\r\n\twidth: 702rpx;\r\n\theight: 624rpx;\r\n\tbackground: linear-gradient(180deg, rgba(2,63,152,0.1) 0%, rgba(2,63,152,0) 100%);\r\n\tborder-radius: 32rpx 32rpx 32rpx 32rpx;\r\n\tmargin: 24rpx;\r\n}\r\n\r\n.skeleton-wrapper {\r\n\tpadding: 24rpx;\r\n\tborder-radius: 16rpx;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\r\n\theight: 100%;\r\n}\r\n\r\n// 轮播卡片容器\r\n.promo-card-wrapper {\r\n\tborder-radius: 16rpx;\r\n\t// overflow: hidden;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\r\n\theight: 100%;\r\n}\r\n\r\n// 轮播组件样式\r\n.promo-swiper {\r\n\twidth: 100%;\r\n\theight: 500rpx; /* 设置固定高度，确保swiper正常显示 */\r\n}\r\n\r\n.swiper-item {\r\n\twidth: 100%;\r\n\theight: 100%; /* 占满swiper容器 */\r\n}\r\n\r\n.promo-card {\r\n\tpadding: 24rpx;\r\n\twidth: 100%;\r\n\theight: 100%; /* 占满swiper-item */\r\n\tbox-sizing: border-box;\r\n\tdisplay: flex;\r\n\tflex-direction: column; /* 垂直布局 */\r\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.promo-header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n\tflex-shrink: 0; /* 防止被压缩 */\r\n\t\r\n\t.promo-icon {\r\n\t\twidth: 44rpx;\r\n\t\theight: 44rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tmargin-right: 12rpx;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\t\r\n\t.promo-title {\r\n\t\twidth: 552rpx;\r\n\t\theight: 44rpx;\r\n\t\tfont-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;\r\n\t\tfont-weight: normal;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #23232A;\r\n\t\ttext-align: left;\r\n\t\tfont-style: normal;\r\n\t\ttext-transform: none;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t\tflex: 1;\r\n\t\tline-height: 44rpx;\r\n\t}\r\n}\r\n\r\n.promo-main-image {\r\n\twidth: 100%;\r\n\tmax-height: 200rpx; /* 限制图片最大高度 */\r\n\tborder-radius: 12rpx;\r\n\tdisplay: block;\r\n\tobject-fit: cover; /* 保持图片比例 */\r\n\tflex-shrink: 0; /* 防止被压缩 */\r\n}\r\n\r\n.promo-description {\r\n\tmargin: 24rpx 0;\r\n\tflex: 1; /* 占据剩余空间 */\r\n\t\r\n\t.desc-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start; /* 图标顶部对齐 */\r\n\t\tmargin-bottom: 16rpx;\r\n\t\t\r\n\t\t&:last-child {\r\n\t\t\tmargin-bottom: 0;\r\n\t\t}\r\n\t}\r\n\t\r\n\t/* [新增] 自定义图标样式 */\r\n\t.desc-icon {\r\n\t    width: 36rpx;\r\n\t    height: 36rpx;\r\n\t    /* 防止图标在flex布局中被压缩变形 */\r\n\t    flex-shrink: 0; \r\n\t}\r\n\t\r\n\t.desc-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #606266;\r\n\t\tmargin-left: 12rpx;\r\n\t\tline-height: 1.4;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 2; /* 最多显示2行 */\r\n\t\toverflow: hidden;\r\n\t}\r\n}\r\n\r\n.promo-footer {\r\n\tflex-shrink: 0; /* 防止被压缩 */\r\n\tmargin-top: auto; /* 推到底部 */\r\n}\r\n\r\n// 自定义指示器样式 - 位于立即报名按钮下方\r\n.custom-indicators {\r\n\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tpadding: 24rpx 0 16rpx 0;\r\n\t\tgap: 12rpx;\r\n}\r\n\r\n.indicator-dot {\r\n\twidth: 24rpx; /* 默认就是长条宽度 */\r\n\t\theight: 12rpx;\r\n\t\tborder-radius: 6rpx; /* 默认就是胶囊圆角 */\r\n\tbackground-color: #e4e7ed; /* 未激活状态的浅灰色 */\r\n\ttransition: background-color 0.3s ease;\r\n\t\r\n\t/* 激活状态 */\r\n\t&.active {\r\n\t\tbackground-color: #004085; /* [颜色修改] 更深的蓝色 */\r\n\t}\r\n}\r\n</style>\r\n", "import Component from 'D:/all code/hongda-wxview/hongda-wxview/components/home/<USER>'\nwx.createComponent(Component)"], "names": ["ref", "getAdListByPositionApi", "uni", "getFullImageUrl", "onMounted"], "mappings": ";;;;;;;;;;;;;;;AA4HA,MAAM,gBAAgB;;;;AALtB,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAC5B,UAAM,YAAYA,cAAAA,IAAI,IAAI;AAC1B,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAU1B,UAAM,iBAAiB,YAAY;AAClC,UAAI;AAEH,cAAM,WAAW,MAAMC,gBAAsB,uBAAC,eAAe;AAAA,UAC5D,UAAU;AAAA,QACb,CAAG;AAEDC,sBAAY,MAAA,MAAA,OAAA,sDAAA,cAAc,QAAQ;AAGlC,YAAI,SAAS,QAAQ,MAAM,QAAQ,SAAS,IAAI,GAAG;AAClD,mBAAS,KAAK,QAAQ,CAAC,IAAI,UAAU;AACpCA,0BAAAA,yEAAY,KAAK,QAAQ,CAAC,aAAa,GAAG,OAAO;AAAA,UACrD,CAAI;AAAA,QACD;AAED,YAAI,SAAS,QAAQ,MAAM,QAAQ,SAAS,IAAI,KAAK,SAAS,KAAK,SAAS,GAAG;AAE9E,wBAAc,QAAQ,SAAS,KAAK,IAAI,QAAM;AAE7C,kBAAM,gBAAgB;AAAA,cACrB,IAAI,GAAG;AAAA,cACP,OAAO,GAAG;AAAA,cACV,OAAOC,YAAAA,gBAAgB,GAAG,QAAQ;AAAA,cAClC,SAASA,YAAAA,gBAAgB,GAAG,OAAO;AAAA;AAAA,cACnC,SAAS,GAAG,gBAAgB,GAAG;AAAA;AAAA;AAAA,cAE/B,kBAAkB,GAAG,gBAAgB;AAAA,cACrC,kBAAkB,GAAG,kBAAkB;AAAA,YAC5C;AAGID,0BAAAA,yEAAY,aAAa;AAAA,cACxB,WAAW,GAAG;AAAA,cACd,YAAY,cAAc;AAAA,cAC1B,OAAO,GAAG;AAAA,YACf,CAAK;AAED,mBAAO;AAAA,UACX,CAAI;AAAA,QACJ,OAAS;AACNA,wBAAa,MAAA,MAAA,QAAA,sDAAA,mBAAmB,QAAQ;AACxC,wBAAc,QAAQ;QACtB;AAAA,MAED,SAAQ,OAAO;AACfA,iGAAc,aAAa,KAAK;AAAA,MAClC,UAAW;AACT,kBAAU,QAAQ;AAAA,MAClB;AAAA,IACF;AAKA,UAAM,iBAAiB,CAAC,MAAM;AAC7B,mBAAa,QAAQ,EAAE,OAAO;AAAA,IAE/B;AAKA,UAAM,gBAAgB,CAAC,UAAU;AAChC,mBAAa,QAAQ;AACrBA,oBAAA,MAAA,MAAA,OAAA,sDAAY,eAAe,KAAK;AAAA,IACjC;AAKA,UAAM,mBAAmB,CAAC,cAAc;AACvC,UAAI,aAAa,UAAU,SAAS;AACnC,cAAM,UAAU,UAAU;AAC1BA,+FAAY,UAAU,OAAO;AAE7B,YAAI,QAAQ,WAAW,MAAM,GAAG;AAC/BA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK,4BAA4B,mBAAmB,OAAO,CAAC,UAAU,mBAAmB,UAAU,SAAS,IAAI,CAAC;AAAA,UACrH,CAAI;AAAA,QACJ,OAAS;AACNA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK;AAAA,UACT,CAAI;AAAA,QACD;AAAA,MACH,OAAQ;AACNA,sBAAAA,MAAa,MAAA,QAAA,sDAAA,YAAY;AAAA,MACzB;AAAA,IACF;AAKA,UAAM,cAAc,CAAC,MAAM;AAC1BA,oBAAAA,MAAA,MAAA,OAAA,sDAAY,QAAQ;AAAA,IACrB;AAKA,UAAM,eAAe,CAAC,MAAM;AAC3BA,oBAAc,MAAA,MAAA,SAAA,sDAAA,WAAW,CAAC;AAAA,IAC3B;AAKA,UAAM,aAAa,CAAC,MAAM;AACzBA,oBAAAA,MAAA,MAAA,OAAA,sDAAY,QAAQ;AAAA,IACrB;AAKA,UAAM,cAAc,CAAC,MAAM;AAC1BA,oBAAc,MAAA,MAAA,SAAA,sDAAA,WAAW,CAAC;AAAA,IAC3B;AAGAE,kBAAAA,UAAU,MAAM;AACf;IACD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3PD,GAAG,gBAAgB,SAAS;"}